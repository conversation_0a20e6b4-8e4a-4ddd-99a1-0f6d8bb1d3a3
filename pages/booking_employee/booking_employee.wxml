<view class="booking-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>
  
  <block wx:else>
    <!-- 订单清单 -->
    <view class="order-summary" wx:if="{{showOrderSummary}}">
      <view class="summary-header">
        <text class="summary-title">订单清单</text>
        <text class="booking-red-tip">
          1.报餐取消时间：当天的09:30前（午餐）<br></br>
          2.报餐取消时间：当天的15:00前（晚餐）<br></br>
          3.报餐取消步骤：点击“订单记录-取消报餐<br></br>
          4.预约较后时段，菜量可能较少，可告知工作人员预留</text>
      </view>
      
      <view class="summary-content">
        <view class="summary-list">
          <view class="summary-item header">
            <text class="item-date">日期</text>
            <text class="item-time">时间段</text>
            <text class="item-people">人数</text>
            <text class="item-price">单价</text>
            <text class="item-fee">费用</text>
          </view>
          
          <view class="summary-item" wx:for="{{orderData}}" wx:key="date">
            <text class="item-date">{{item.date}}</text>
            <text class="item-time">{{item.timeSlot}}</text>
            <text class="item-people">{{item.people}}人</text>
            <text class="item-price">¥{{item.mealPrice}}</text>
            <text class="item-fee">¥{{item.people * (item.mealPrice + reservationFee)}}</text>
          </view>
        </view>
        
        <view class="reservation-fee-info" wx:if="{{reservationFee > 0}}">
          <text>*包含每人¥{{reservationFee}}预订费</text>
        </view>

        <!-- 优惠券选择 -->
        <view class="coupon-section">
          <view class="coupon-row" bindtap="showCouponList">
            <text class="coupon-label">优惠券</text>
            <view class="coupon-content">
              <text class="coupon-text" wx:if="{{selectedCoupons.length === 0}}">请选择优惠券</text>
              <text class="coupon-text selected" wx:else>已选择{{selectedCoupons.length}}张优惠券</text>
              <text class="coupon-arrow">></text>
            </view>
          </view>
          <view class="coupon-discount" wx:if="{{couponDiscount > 0}}">
            <text class="discount-label">优惠券折扣</text>
            <text class="discount-amount">-¥{{couponDiscount}}</text>
          </view>
        </view>

        <view class="total-row">
          <text class="total-label">总计</text>
          <text class="total-amount">¥{{finalAmount || totalAmount}}</text>
        </view>
      </view>
      
      <view class="summary-actions">
        <button class="back-btn" bindtap="backToEdit">返回修改</button>
        <button class="confirm-order-btn" bindtap="confirmOrder">确认订单</button>
      </view>
    </view>
    
    <!-- 订餐表单 -->
    <block wx:else>
      <!-- 预订费用信息 -->
      <view class="fee-info" wx:if="{{reservationFee > 0}}">
        <text>预订费用: ¥{{reservationFee}}</text>
      </view>

      <!-- 餐食类型切换 - 根据配置显示 -->
      <view class="meal-type-switch" wx:if="{{showMealTypeSwitch}}">
        <!-- 下拉切换选择器 -->
        <view class="dropdown-selector" wx:if="{{source !== 'admin'}}">
          <view class="dropdown-header" bindtap="toggleDropdown">
            <text class="dropdown-text">{{currentPageType}}</text>
            <view class="dropdown-arrow {{showDropdown ? 'up' : 'down'}}">▼</view>
          </view>
          
          <view class="dropdown-menu {{showDropdown ? 'show' : ''}}" wx:if="{{showDropdown}}">
            <view class="dropdown-item active" bindtap="closeDropdown">
              <text>企业餐</text>
            </view>
            <view class="dropdown-item" bindtap="switchToPersonal">
              <text>个人餐</text>
            </view>
          </view>
        </view>
        
        <view class="meal-type-tab {{activeMealType === 'lunch' ? 'active' : ''}}"
              wx:if="{{showLunch}}"
              bindtap="switchMealType"
              data-type="lunch">午餐</view>
        <view class="meal-type-tab {{activeMealType === 'dinner' ? 'active' : ''}}"
              wx:if="{{showDinner}}"
              bindtap="switchMealType"
              data-type="dinner">晚餐</view>
      </view>

      <!-- 表头 -->
      <view class="table-header">
        <text class="header-item">日期</text>
        <text class="header-item">人数</text>
        <text class="header-item">就餐时段</text>
      </view>

      <!-- 日期列表 -->
      <view class="date-list">
        <view class="date-item" wx:for="{{dateList}}" wx:key="date" wx:for-index="idx">
          <text class="date">{{item.date_str}}</text>

          <!-- 人数显示区域（企业餐固定为1人） -->
          <view class="people-display">
            <text class="people-text">1</text>
          </view>

          <button class="action-btn select-time-btn {{item.canSelect ? 'active' : 'disabled'}}"
                  bindtap="{{item.canSelect ? 'selectTime' : ''}}"
                  data-index="{{idx}}">
            {{item.timeSlot || (item.canSelect ? '请选择' : (item.unavailableReason || '已有预订记录'))}}
          </button>
        </view>
      </view>
      
      <!-- 时间段选择区域 -->
      <view class="time-slot-section" wx:if="{{showTimeSlots}}">
        <view class="date-time-row">
          <view class="selected-date">{{selectedDate}}</view>
          <view class="selected-date">就餐时段：{{selectedTimeSlot || '请选择'}}</view>
          <view class="close-icon" bindtap="closeTimeSlots">×</view>
        </view>
        <view class="time-slots">
          <view class="time-slot {{slot.disabled ? 'disabled' : ''}} {{slot.selected ? 'selected' : ''}}"
                wx:for="{{currentTimeSlots}}" 
                wx:key="time" 
                wx:for-item="slot"
                bindtap="{{!slot.disabled ? 'selectTimeSlot' : ''}}"
                data-time="{{slot.time}}">
            <text>{{slot.time}}</text>
            <text class="remaining">{{slot.remaining}}</text>
          </view>
        </view>
      </view>

      <!-- 底部确认按钮 -->
      <view class="bottom-actions">
        <button class="cancel-btn" bindtap="handleCancel">取消</button>
        <button class="confirm-btn" bindtap="submitBooking">确认</button>
      </view>
    </block>
  </block>

  <!-- 企业选择弹窗 -->
  <view class="payment-modal" wx:if="{{showPaymentOptions}}">
    <view class="payment-container">
      <view class="payment-header">
        <text>实付金额</text>
        <view class="close-icon" bindtap="closePayment">×</view>
      </view>
      <view class="payment-amount">¥{{payableAmount}}</view>
      
      <view class="payment-methods">
        <text>选择企业:</text>

        <!-- 晚餐限制提示 -->
        <view class="dinner-notice" wx:if="{{activeMealType === 'dinner'}}">
          <text class="notice-text">⚠️ 晚餐时段仅限以下企业支付</text>
        </view>

        <view class="payment-method {{selectedEnterprise === enterprise.id ? 'selected' : ''}}"
              wx:for="{{enterpriseList}}" 
              wx:key="id" 
              wx:for-item="enterprise"
              bindtap="selectEnterprise" 
              data-id="{{enterprise.id}}">
          <text class="enterprise-name">{{enterprise.company_name}}</text>
          <view class="radio-btn"></view>
        </view>

        <!-- 如果没有可用企业的提示 -->
        <view class="no-enterprise" wx:if="{{enterpriseList.length === 0}}">
          <text>{{activeMealType === 'dinner' ? '晚餐暂无可用企业' : '暂无可用企业'}}</text>
        </view>
      </view>
      
      <button class="confirm-payment-btn" bindtap="confirmPayment">确认支付</button>
    </view>
  </view>

  <!-- 优惠券列表组件 -->
  <coupon-list
    visible="{{showCouponList}}"
    order-amount="{{totalAmount}}"
    selected-coupon-ids="{{selectedCouponIds}}"
    allow-multiple="{{true}}"
    products="{{products}}"
    bind:close="hideCouponList"
    bind:select="onCouponSelect"
  ></coupon-list>
</view>