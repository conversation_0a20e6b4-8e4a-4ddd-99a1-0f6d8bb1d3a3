import { checkLogin, getUserInfo } from '../../service/user';

Page({
  data: {
    dateList: [],
    showTimeSlots: false,
    selectedDate: '',
    selectedTimeSlot: '',
    selectedDateIndex: -1,
    isBatchSetting: false,
    currentTimeSlots: [],
    loading: true,  // 添加加载状态
    reservationFee: 0,  // 预订费用
    showOrderSummary: false,  // 添加订单清单显示状态
    orderData: null,  // 添加订单数据
    totalAmount: 0,    // 添加总金额
    mealPrices: {},     // 存储每个餐点的价格
    userBalance: 0,      // 用户余额
    enterpriseList: [],  // 企业列表
    selectedEnterprise: null, // 选中的企业ID
    source: 'unknown',  // 添加source参数
    mealTypes: ['lunch', 'dinner'],  // 支持的餐食类型
    activeMealType: 'lunch',         // 默认显示午餐
    dateListByMealType: {            // 按餐食类型分类的日期列表
      lunch: [],
      dinner: []
    },
    // 新增：餐食类型配置
    mealTypeConfig: {},
    availableMealTypes: [],  // 可用的餐食类型
    showMealTypeSwitch: false,  // 是否显示餐食类型切换按钮
    showLunch: false, // 控制午餐餐食类型是否显示
    showDinner: false, // 控制晚餐餐食类型是否显示
    // 新增：开放企业列表
    openEnterpriseList: [],
    // 新增：晚餐权限
    has_dinner_permission: true,
    showDropdown: false,  // 控制下拉菜单显示
    currentPageType: '企业餐',  // 当前页面类型显示
    // 优惠券相关数据
    showCouponList: false,  // 是否显示优惠券列表
    selectedCoupons: [],     // 选中的优惠券列表（支持多选）
    selectedCouponIds: [],   // 选中的优惠券ID列表
    couponDiscount: 0,      // 优惠券总折扣金额
    finalAmount: 0          // 最终金额（扣除优惠券后）
  },
  
  // 页面加载时获取预订信息
  onLoad: function(options) {
    console.log('options', options)
    // 保存来源信息
    this.setData({
      source: options.source || 'unknown',
      activeMealType: options.mealType || 'lunch'  // 可以通过参数指定默认显示的餐食类型
    });
    
    // 首先获取餐食类型配置，然后获取预订信息
    this.getMealTypeConfig();

    // 获取开放企业列表
    this.getOpenEnterpriseList();
  },

  // 新增：获取开放企业列表
  getOpenEnterpriseList: function() {
    const that = this;
    wx.request({
      url: `${getApp().globalData.baseUrl}/config/get_open_enterprise_list`,
      method: 'GET',
      success: function(res) {
        console.log('获取开放企业列表响应:', res.data);

        if (Array.isArray(res.data)) {
          that.setData({
            openEnterpriseList: res.data
          });
          console.log('开放企业列表:', res.data);
        }
      },
      fail: function(err) {
        console.error('获取开放企业列表失败', err);
        // 使用空数组作为默认值，不影响主流程
        that.setData({
          openEnterpriseList: []
        });
      }
    });
  },

  // 获取餐食类型配置
  getMealTypeConfig: function() {
    const that = this;
    wx.request({
      url: `${getApp().globalData.baseUrl}/config/get_config`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      success: function(res) {
        if (res.data) {
          const config = res.data;
          console.log('餐食类型配置:', config);

          // 筛选出启用的餐食类型（值为1的）
          const availableMealTypes = [];
          let showMealTypeSwitch = false;

          // 检查LUNCH配置（注意这里是大写）
          if (config.LUNCH === 1) {
            console.log('启用午餐');
            availableMealTypes.push('lunch');
          }

          // 检查DINNER配置（注意这里是大写）
          if (config.DINNER === 1) {
            console.log('启用晚餐');
            availableMealTypes.push('dinner');
          }

          // 如果有多个餐食类型可用，显示切换按钮
          showMealTypeSwitch = availableMealTypes.length > 1;

          console.log('可用餐食类型:', availableMealTypes);
          console.log('availableMealTypes.length:', availableMealTypes.length);
          console.log('是否显示切换按钮:', showMealTypeSwitch);

          // 如果当前选中的餐食类型不可用，切换到第一个可用的
          let currentMealType = that.data.activeMealType;
          if (!availableMealTypes.includes(currentMealType) && availableMealTypes.length > 0) {
            currentMealType = availableMealTypes[0];
          }

          console.log('当前餐食类型:', currentMealType);

          that.setData({
            mealTypeConfig: config,
            availableMealTypes: availableMealTypes,
            showMealTypeSwitch: showMealTypeSwitch,
            activeMealType: currentMealType,
            showLunch: availableMealTypes.includes('lunch'),
            showDinner: availableMealTypes.includes('dinner')
          });

          // 添加setData后的调试日志
          console.log('setData后的页面数据:');
          console.log('availableMealTypes:', that.data.availableMealTypes);
          console.log('showMealTypeSwitch:', that.data.showMealTypeSwitch);
          console.log('activeMealType:', that.data.activeMealType);
          console.log('showLunch:', that.data.showLunch);
          console.log('showDinner:', that.data.showDinner);

          // 如果没有可用的餐食类型，显示提示
          if (availableMealTypes.length === 0) {
            wx.showToast({
              title: '暂无可用餐食类型',
              icon: 'none'
            });
            that.setData({
              loading: false
            });
            return;
          }

          // 获取预订信息
          that.getBookingInfo();
        } else {
          console.error('获取餐食类型配置失败');
          // 如果获取配置失败，使用默认配置继续
          that.setData({
            availableMealTypes: ['lunch', 'dinner'],
            showMealTypeSwitch: true,
            showLunch: true,
            showDinner: true
          });
          that.getBookingInfo();
        }
      },
      fail: function(err) {
        console.error('获取餐食类型配置失败', err);
        // 如果获取配置失败，使用默认配置继续
        that.setData({
          availableMealTypes: ['lunch', 'dinner'],
          showMealTypeSwitch: true,
          showLunch: true,
          showDinner: true
        });
        that.getBookingInfo();
      }
    });
  },
  
  // 获取预订信息
  getBookingInfo: function() {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });
    
    wx.request({
      url: `${getApp().globalData.baseUrl}/reserve/config`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        type: 'employee',
        source: this.data.source || 'unknown'  // 添加source参数
      },
      success: function(res) {
        if (res.data.code === 200) {
          const data = res.data.data;
          let dateList = data.dateList;
          const alreadyDateList = data.already_date_list || [];
          
          // 如果来源是admin，只显示第一条数据
          if (that.data.source === 'admin' && dateList.length > 0) {
            dateList = [dateList[0]];
          }

          // 按餐食类型分类，只处理可用的餐食类型
          const dateListByMealType = {
            lunch: [],
            dinner: []
          };

          // 处理每一天的数据
          dateList.forEach(dateItem => {
            // 复制日期基本信息
            const lunchDateItem = {...dateItem, timeSlots: []};
            const dinnerDateItem = {...dateItem, timeSlots: []};

            // 将时间段按餐食类型分类
            dateItem.timeSlots.forEach(slot => {
              if (slot.rule_meal_type === 'lunch' && that.data.availableMealTypes.includes('lunch')) {
                lunchDateItem.timeSlots.push(slot);
              } else if (slot.rule_meal_type === 'dinner' && that.data.availableMealTypes.includes('dinner')) {
                dinnerDateItem.timeSlots.push(slot);
              }
            });

            // 处理午餐数据 - 添加所有日期，不管是否有时间段
            if (that.data.availableMealTypes.includes('lunch')) {
              // 为每个日期设置初始人数为1，但不设置选中状态
              lunchDateItem.people = 1;

              // 检查该日期的午餐预订状态 - 同时检查 already_date_list 和当前日期的 meal_type_status
              const alreadyBookedItem = alreadyDateList.find(item => item.date === lunchDateItem.date);
              const hasLunchReservationFromAlready = alreadyBookedItem && 
                                                   alreadyBookedItem.meal_type_status && 
                                                   alreadyBookedItem.meal_type_status.lunch === 'has_reservation';
              const noLunchPermissionFromAlready = alreadyBookedItem && 
                                                 alreadyBookedItem.meal_type_status && 
                                                 alreadyBookedItem.meal_type_status.lunch === 'no_permission';
              
              // 检查当前日期项的 meal_type_status (这是关键！)
              const hasLunchReservationFromCurrent = dateItem.meal_type_status && 
                                                    dateItem.meal_type_status.lunch === 'has_reservation';
              const noLunchPermissionFromCurrent = dateItem.meal_type_status && 
                                                  dateItem.meal_type_status.lunch === 'no_permission';

              // 综合判断
              const hasLunchReservation = hasLunchReservationFromAlready || hasLunchReservationFromCurrent;
              const noLunchPermission = noLunchPermissionFromAlready || noLunchPermissionFromCurrent;

              // 如果没有时间段或午餐已预订，设置为不可选中
              if (lunchDateItem.timeSlots.length === 0 || hasLunchReservation || noLunchPermission) {
                lunchDateItem.canSelect = false;
                
                // 设置不可选择的原因文本 - 针对午餐
                if (hasLunchReservation) {
                  lunchDateItem.unavailableReason = '已有预订记录';
                } else if (noLunchPermission) {
                  lunchDateItem.unavailableReason = '预订已截止';
                } else if (lunchDateItem.timeSlots.length === 0) {
                  lunchDateItem.unavailableReason = '预订已截止';
                }
              }

              // 只添加不是"预订已截止"状态的日期项，或者是"已有预订记录"的日期项
              if (lunchDateItem.unavailableReason !== '预订已截止') {
                dateListByMealType.lunch.push(lunchDateItem);
              }
            }

            // 处理晚餐数据 - 添加所有日期，不管是否有时间段
            if (that.data.availableMealTypes.includes('dinner')) {
              // 为每个日期设置初始人数为1，但不设置选中状态
              dinnerDateItem.people = 1;

              // 检查该日期的晚餐预订状态 - 同时检查 already_date_list 和当前日期的 meal_type_status
              const alreadyBookedItem = alreadyDateList.find(item => item.date === dinnerDateItem.date);
              const hasDinnerReservationFromAlready = alreadyBookedItem && 
                                                    alreadyBookedItem.meal_type_status && 
                                                    alreadyBookedItem.meal_type_status.dinner === 'has_reservation';
              const noDinnerPermissionFromAlready = alreadyBookedItem && 
                                                  alreadyBookedItem.meal_type_status && 
                                                  alreadyBookedItem.meal_type_status.dinner === 'no_permission';
              
              // 检查当前日期项的 meal_type_status (这是关键！)
              const hasDinnerReservationFromCurrent = dateItem.meal_type_status && 
                                                     dateItem.meal_type_status.dinner === 'has_reservation';
              const noDinnerPermissionFromCurrent = dateItem.meal_type_status && 
                                                   dateItem.meal_type_status.dinner === 'no_permission';

              // 综合判断
              const hasDinnerReservation = hasDinnerReservationFromAlready || hasDinnerReservationFromCurrent;
              const noDinnerPermission = noDinnerPermissionFromAlready || noDinnerPermissionFromCurrent;

              // 如果没有时间段或晚餐已预订，设置为不可选中
              if (dinnerDateItem.timeSlots.length === 0 || hasDinnerReservation || noDinnerPermission) {
                dinnerDateItem.canSelect = false;
                
                // 设置不可选择的原因文本 - 针对晚餐
                if (hasDinnerReservation) {
                  dinnerDateItem.unavailableReason = '已有预订记录';
                } else if (noDinnerPermission) {
                  dinnerDateItem.unavailableReason = '预订已截止';
                } else if (dinnerDateItem.timeSlots.length === 0) {
                  // 检查是否是权限问题
                  if (!that.data.has_dinner_permission) {
                    dinnerDateItem.unavailableReason = '预订已截止';
                  } else {
                    dinnerDateItem.unavailableReason = '预订已截止';
                  }
                }
              }

              // 只添加不是"预订已截止"状态的日期项，或者是"已有预订记录"的日期项
              if (dinnerDateItem.unavailableReason !== '预订已截止') {
                dateListByMealType.dinner.push(dinnerDateItem);
              }
            }
          });
          
          // 保存企业列表（保存原始企业列表，在支付时再过滤）
          that.setData({
            dateList: dateListByMealType[that.data.activeMealType] || [],
            dateListByMealType: dateListByMealType,
            reservationFee: data.reservationFee,
            loading: false,
            selectedDate: '',  // 清空选中日期
            selectedDateIndex: -1,  // 清空选中索引
            enterpriseList: data.enterprise_list || [], // 保存企业列表
            has_dinner_permission: data.has_dinner_permission !== false // 保存晚餐权限，默认为true
          });
        } else {
          // 请求失败
          wx.showToast({
            title: res.data.message || '获取预订信息失败',
            icon: 'none'
          });
          that.setData({
            loading: false
          });
        }
      },
      fail: function(err) {
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
        that.setData({
          loading: false
        });
      },
      complete: function() {
        wx.hideLoading();
      }
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.getBookingInfo();
    wx.stopPullDownRefresh();
  },

  // 选择日期并打开时间段选择
  selectTime(e) {
    // 确保能获取到 index
    const index = e.currentTarget.dataset.index;
    
    if (index === undefined) {
      wx.showToast({
        title: '获取日期信息失败',
        icon: 'none'
      });
      return;
    }
    
    const date = this.data.dateList[index].date;
    const timeSlot = this.data.dateList[index].timeSlot;
    
    // 如果该日期已经有选择的时间段，则取消选择
    if (timeSlot) {
      const updatedDateList = [...this.data.dateList];
      updatedDateList[index] = {
        ...updatedDateList[index],
        timeSlot: '',  // 清除选择的时间段
        selected: false,  // 取消选中状态
        mealPrice: 0,  // 重置餐点价格
        maxPeople: 0   // 重置最大可预订人数
      };

      this.setData({
        dateList: updatedDateList
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'success',
        duration: 1000
      });
      return;
    }

    // 使用选中日期的时间段列表
    let currentTimeSlots = JSON.parse(JSON.stringify(this.data.dateList[index].timeSlots));

    // 如果没有选择的时间段，清除所有选中状态
    currentTimeSlots = currentTimeSlots.map(slot => ({
      ...slot,
      selected: false
    }));
    
    this.setData({
      showTimeSlots: true,
      selectedDate: date,
      selectedDateIndex: index,
      selectedTimeSlot: '',
      currentTimeSlots,
      isBatchSetting: false
    });
  },

  // 选择时间段
  selectTimeSlot(e) {
    const time = e.currentTarget.dataset.time;
    const { dateList, selectedDateIndex, isBatchSetting } = this.data;
    
    // 找到选中时间段的价格
    const selectedSlot = this.data.currentTimeSlots.find(slot => slot.time === time);
    const price = selectedSlot ? selectedSlot.price : 0;
    console.log(selectedSlot.rule_id)
    console.log(selectedSlot.product_id)
    
    // 先更新选中状态
    const currentTimeSlots = this.data.currentTimeSlots.map(slot => ({
      ...slot,
      selected: slot.time === time
    }));
    
    // 设置选中的时间段
    this.setData({ 
      currentTimeSlots,
      selectedTimeSlot: time
    });
    
    // 立即确认选择
    if (isBatchSetting) {
      // 批量设置时间段
      const updatedDateList = dateList.map(item => {
        if (item.selected && item.canSelect) {
          // 检查该日期是否有这个时间段，且不是已约满
          const timeSlotItem = item.timeSlots.find(slot => slot.time === time);
          const hasTimeSlot = timeSlotItem && !timeSlotItem.disabled;
          
          if (hasTimeSlot) {
            // 更新时间段的选中状态和价格
            const updatedTimeSlots = item.timeSlots.map(slot => ({
              ...slot,
              selected: slot.time === time
            }));
            
            return {
              ...item,
              timeSlot: time,
              timeSlots: updatedTimeSlots,
              mealPrice: timeSlotItem.price // 添加餐点价格
            };
          } else {
            // 如果该日期的这个时间段已约满，保持为"请选择"状态
            return {
              ...item,
              timeSlot: '',  // 重置为未选择状态
              timeSlots: item.timeSlots.map(slot => ({
                ...slot,
                selected: false
              })),
              mealPrice: 0 // 重置餐点价格
            };
          }
        }
        return item;
      });
      
      this.setData({
        dateList: updatedDateList,
        showTimeSlots: false,
        isBatchSetting: false
      });
    } else {
      // 单个日期设置时间段
      if (selectedDateIndex !== undefined && selectedDateIndex >= 0 && selectedDateIndex < dateList.length) {
        const updatedDateList = [...dateList];
        updatedDateList[selectedDateIndex] = {
          ...updatedDateList[selectedDateIndex],
          timeSlot: time,
          selected: true,
          timeSlots: currentTimeSlots,
          mealPrice: price // 添加餐点价格
        };
        
        this.setData({
          dateList: updatedDateList,
          showTimeSlots: false
        });
      }
    }
  },

  // 取消选择时间段
  onCancel() {
    this.setData({
      showTimeSlots: false,
      isBatchSetting: false
    });
  },

  // 输入人数
  inputPeople(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    
    if (index === undefined) return;
    
    // 更新对应日期的人数
    const dateList = [...this.data.dateList];
    dateList[index].people = value;
    
    this.setData({ dateList });
  },

  // 切换选中状态
  toggleSelect(e) {
    const index = e.currentTarget.dataset.index;
    
    if (index === undefined) return;
    
    // 更新对应日期的选中状态
    const dateList = [...this.data.dateList];
    dateList[index].selected = !dateList[index].selected;
    
    this.setData({ dateList });
  },

  // 全选功能
  selectAll() {
    const dateList = this.data.dateList.map(item => {
      // 只对可选择的日期设置选中状态
      if (item.canSelect) {
        return {
          ...item,
          selected: true
        };
      }
      return item;
    });
    
    this.setData({ dateList });
  },

  // 批量选择时间段
  batchSelectTime() {
    // 获取所有选中且可选择的日期
    const selectedDates = this.data.dateList.filter(item => item.selected && item.canSelect);
    
    if (selectedDates.length === 0) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      });
      return;
    }
    
    // 创建一个合并的时间段列表（取所有选中日期的时间段的交集）
    let mergedTimeSlots = [];
    
    // 初始化为第一个选中日期的时间段
    if (selectedDates.length > 0) {
      mergedTimeSlots = JSON.parse(JSON.stringify(selectedDates[0].timeSlots));
      
      // 与其他选中日期的时间段取交集
      for (let i = 1; i < selectedDates.length; i++) {
        const currentSlots = selectedDates[i].timeSlots;
        
        // 只保留两个列表中都存在的时间段
        mergedTimeSlots = mergedTimeSlots.filter(slot => {
          const matchingSlot = currentSlots.find(s => s.time === slot.time);
          return matchingSlot && !matchingSlot.disabled;
        });
      }
      
      // 清除选中状态
      mergedTimeSlots = mergedTimeSlots.map(slot => ({
        ...slot,
        selected: false
      }));
    }
    
    // 弹出时间段选择
    this.setData({
      showTimeSlots: true,
      selectedDate: '批量设置',
      currentTimeSlots: mergedTimeSlots,
      selectedTimeSlot: '',
      isBatchSetting: true
    });
  },

  // 重置功能
  reset() {
    // 保留原有的 timeSlots 列表，但重置其他属性
    const dateList = this.data.dateList.map(item => {
      const updatedTimeSlots = item.timeSlots.map(slot => ({
        ...slot,
        selected: false
      }));
      
      return {
        ...item,
        people: '',
        selected: false,
        timeSlot: '',
        timeSlots: updatedTimeSlots
      };
    });
    
    this.setData({ dateList });
  },

  // 提交表单
  submitBooking() {
    // 验证是否有选中的日期
    const hasSelected = this.data.dateList.some(item => item.selected);
    
    if (!hasSelected) {
      wx.showToast({
        title: '请至少选择一天',
        icon: 'none'
      });
      return;
    }
    
    // 收集所有没有填写完整信息的日期
    const incompleteItems = this.data.dateList.filter(item => {
      // 确保选中的项目有时间段，人数固定为1，不再检查
      return item.selected && !item.timeSlot;
    });
    
    if (incompleteItems.length > 0) {
      // 找出第一个未填写完整的日期，只提示缺少时间段
      const firstIncomplete = incompleteItems[0];
      let message = `${firstIncomplete.date} 的就餐时段未选择`;
      
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 新增：检查每个选中日期的人数是否超过最大可预订人数
    const exceededItems = this.data.dateList.filter(item => {
      if (item.selected) {
        const selectedTimeSlot = item.timeSlots.find(slot => slot.time === item.timeSlot);
        return selectedTimeSlot && parseInt(item.people) > selectedTimeSlot.remaining_int;
      }
      return false;
    });

    if (exceededItems.length > 0) {
      const firstExceeded = exceededItems[0];
      wx.showToast({
        title: `${firstExceeded.date} 该时段人数超额`,
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 准备要提交的数据
    const bookings = this.data.dateList
      .filter(item => item.selected)
      .map(item => ({
        date: item.date.split(' ')[0],  // 只取日期部分, 如 "01-07"
        people: 1,  // 固定为1人
        timeSlot: item.timeSlot,
        mealPrice: item.mealPrice || 0,  
        product_id: item.timeSlots.find(slot => slot.time === item.timeSlot)?.product_id,
        rule_id: item.timeSlots.find(slot => slot.time === item.timeSlot)?.rule_id,
        rule_item_id: item.timeSlots.find(slot => slot.time === item.timeSlot)?.rule_item_id,
        reservation_period: item.timeSlots.find(slot => slot.time === item.timeSlot)?.reservation_period,
        quantity: 1  // 固定为1人
      }));

    // 计算总费用（预订费用 + 餐点费用）
    let totalAmount = 0;
    bookings.forEach(booking => {
      // 预订费用 - 直接相乘不四舍五入
      const reservationTotal = booking.people * this.data.reservationFee;
      // 餐点费用 - 直接相乘不四舍五入
      const mealTotal = booking.people * booking.mealPrice;
      totalAmount += reservationTotal + mealTotal;
    });
    
    // 最终金额才四舍五入为两位小数
    totalAmount = Number(totalAmount.toFixed(2));
    
    // 显示订单清单
    this.setData({
      showOrderSummary: true,
      orderData: bookings,
      totalAmount: totalAmount
    });
  },
  
  // 确认下单
  confirmOrder() {
    // 提交数据
    wx.showLoading({
      title: '提交中...',
    });
    
    wx.request({
      url: `${getApp().globalData.baseUrl}/reserve/submit`,
      method: 'POST',
      header: {
        'token': wx.getStorageSync('token')
      },
              data: {
          bookings: this.data.orderData,
          coupon_id: this.data.selectedCoupons.map(coupon => coupon.coupon_usage_record.id).join(','),
          coupon_discount: this.data.couponDiscount || 0
        },
      success: (res) => {
        wx.hideLoading();
        console.log("!!!!!")
        console.log(res.data)
        if (res.data.status === 200) {
          // 显示支付选择弹窗
          console.log(res.data.reservation_result)
          this.showPaymentOptions(res.data.reservation_result);
        } else {
          wx.showToast({
            title: res.data.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 显示支付选择弹窗替换为企业选择弹窗
  showPaymentOptions(orderData) {
    let enterpriseList = [...this.data.enterpriseList]; // 复制原始企业列表

    console.log('当前餐次类型:', this.data.activeMealType);
    console.log('原始企业列表:', enterpriseList.map(e => e.company_name));

    // 如果是晚餐，过滤企业列表
    if (this.data.activeMealType === 'dinner') {
      const openEnterpriseNames = this.data.openEnterpriseList;
      console.log('开放企业名单:', openEnterpriseNames);

      enterpriseList = enterpriseList.filter(enterprise => {
        const isOpen = openEnterpriseNames.includes(enterprise.company_name);
        console.log(`企业 ${enterprise.company_name} 是否在开放名单中:`, isOpen);
        return isOpen;
      });

      console.log('晚餐过滤后企业列表:', enterpriseList.map(e => e.company_name));

      // 如果晚餐时没有可用企业，显示提示
      if (enterpriseList.length === 0) {
        wx.showModal({
          title: '支付提示',
          content: '晚餐时段您的企业暂不支持企业支付，请联系管理员或选择其他时段',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            // 不显示支付弹窗，返回修改
            this.backToEdit();
          }
        });
        return;
      }
    }

    this.setData({
      showPaymentOptions: true,
      paymentOrderData: orderData,
      enterpriseList: enterpriseList, // 使用过滤后的企业列表
      selectedEnterprise: enterpriseList.length > 0 ? enterpriseList[0].id : null,
      payableAmount: orderData.payable_amount
    });
  },

  // 替换选择支付方式为选择企业
  selectEnterprise(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      selectedEnterprise: id
    });
  },

  // 修改确认支付方法
  confirmPayment() {
    const { selectedEnterprise, paymentOrderData, totalAmount } = this.data;
    
    if (!selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '处理中...',
    });

    console.log("!!!!!!! 企业 ID !!!!!!!")
    console.log(selectedEnterprise)
    
    wx.request({
      url: `${getApp().globalData.baseUrl}/order/pay/create`,
      method: 'POST',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        order_no: paymentOrderData.order_no,
        enterprise_id: selectedEnterprise,
        paymentMethod: 'enterprise',
        amount: totalAmount
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 200) {
          this.paymentSuccess();
        } else {
          wx.showToast({
            title: res.data.message || '支付失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 微信支付成功处理
  paymentSuccess() {
    // 刷新用户余额和预订列表
    this.refreshUserInfoAndBookings();
    
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        this.setData({
          showPaymentOptions: false
        });
        
        // 根据来源决定跳转方式
        setTimeout(() => {
          if (this.data.source === 'admin') {
            // 如果是扫码进入的，跳转到预订主页
            wx.switchTab({
              url: '/pages/topic/topic'
            });
          } else {
            // 正常返回上一页
            wx.navigateBack({
              delta: 1
            });
          }
        }, 2000);
      }
    });
  },

  // 刷新用户余额和预订列表
  refreshUserInfoAndBookings() {
    const app = getApp();
    
    // 使用service/user.js中的getUserInfo方法刷新用户信息
    getUserInfo().then(userInfo => {
      // 更新全局用户信息
      if (app.globalData.userInfo) {
        app.globalData.userInfo.balance = userInfo.balance || 0;
      }
      
      // 更新页面数据
      this.setData({
        userBalance: userInfo.balance || 0
      });
    }).catch(err => {
      console.error('获取用户信息失败', err);
    });
    
    // 刷新预订列表（通过页面事件通知）
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 上一个页面
    if (prevPage && prevPage.refreshBookingList) {
      prevPage.refreshBookingList();
    }
  },

  // 关闭支付弹窗
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });
  },

  // 返回修改
  backToEdit() {
    this.setData({
      showOrderSummary: false
    });
  },

  // 取消
  handleCancel() {
    console.log('取消按钮被点击');
    
    // 如果是从 scan_redirect 页面跳转来的，需要跳转到首页
    if (this.data.source === 'admin') {
      wx.switchTab({
        url: '/pages/index/index',
        success: () => {
          console.log('跳转到首页成功');
        },
        fail: (error) => {
          console.error('跳转失败：', error);
          // 如果跳转失败，尝试返回上一页
          wx.navigateBack({
            delta: 1
          });
        }
      });
    } else {
      // 正常返回上一页
    wx.navigateBack({
      delta: 1
      });
    }
  },

  // 关闭时间段选择
  closeTimeSlots() {
    this.setData({
      showTimeSlots: false,
      isBatchSetting: false
    });
  },

  // 切换餐食类型
  switchMealType: function(e) {
    const mealType = e.currentTarget.dataset.type;

    // 检查该餐食类型是否可用
    if (!this.data.availableMealTypes.includes(mealType)) {
      return;
    }

    this.setData({
      activeMealType: mealType,
      dateList: this.data.dateListByMealType[mealType] || [],
      selectedDate: '',
      selectedDateIndex: -1,
      showTimeSlots: false
    });
  },

  // 新增：切换到个人餐页面
  switchToPersonal: function() {
    wx.navigateTo({
      url: '/pages/booking/booking?source=topic&mealType=' + this.data.activeMealType,
      fail: (error) => {
        console.error('页面跳转失败：', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取用户信息
  getUserInfo: function() {
    const app = getApp();
    if (app.globalData && app.globalData.userInfo) {
      this.setData({
        userBalance: app.globalData.userInfo.balance || 0
      });
    } else {
      wx.request({
        url: `${app.globalData.baseUrl}/user/info`,
        header: {
          'token': wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.setData({
              userBalance: res.data.data.balance || 0
            });
          }
        }
      });
    }
  },

  // 查看菜单
  viewMenu: function(e) {
    // 获取索引 
    const index = e.currentTarget.dataset.index;
    const date = e.currentTarget.dataset.date;
    
    // 跳转到菜单页面
    wx.navigateTo({
      url: `/pages/menu/menu?menu_list=${encodeURIComponent(JSON.stringify(this.data.dateList[index].timeSlots))}&item_date=${encodeURIComponent(date)}`
    });
  },

  // 添加日期格式化函数
  formatDateStr(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[date.getDay()];
    return `${year}-${month}-${day} ${weekday}`;
  },

  // 下拉选择器相关方法
  toggleDropdown: function() {
    this.setData({
      showDropdown: !this.data.showDropdown
    });
  },

  closeDropdown: function() {
    this.setData({
      showDropdown: false
    });
  },

  // 优惠券相关方法
  // 生成产品数量列表
  generateProductList() {
    const products = [];

    // 从orderData中提取产品信息
    if (this.data.orderData && this.data.orderData.length > 0) {
      this.data.orderData.forEach(booking => {
        if (booking.product_id && booking.quantity > 0) {
          products.push({
            product_id: booking.product_id,
            quantity: booking.quantity
          });
        }
      });
    }

    return products;
  },

  /**
   * 显示优惠券列表
   */
  showCouponList() {
    // 检查是否有订单数据
    if (!this.data.orderData || this.data.orderData.length === 0) {
      wx.showToast({
        title: '请先选择预订信息',
        icon: 'none'
      });
      return;
    }

    // 确保用户信息已加载
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    if (products.length === 0) {
      wx.showToast({
        title: '暂无可用产品信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCouponList: true,
      products: products  // 保存产品列表供组件使用
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 计算所有选中优惠券的总折扣金额
      let totalDiscount = 0;
      coupons.forEach(coupon => {
        totalDiscount += this.calculateCouponDiscount(coupon);
      });
      
      const finalAmount = Math.max(0, this.data.totalAmount - totalDiscount);

      this.setData({
        selectedCoupons: coupons,
        selectedCouponIds: couponIds, // 更新选中的优惠券ID列表
        couponDiscount: totalDiscount,
        finalAmount: finalAmount,
        showCouponList: false
      });

      wx.showToast({
        title: `已选择${coupons.length}张优惠券`,
        icon: 'success'
      });
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [], // 清空选中的优惠券ID列表
        couponDiscount: 0,
        finalAmount: this.data.totalAmount,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  /**
   * 计算优惠券折扣金额
   */
  calculateCouponDiscount(couponData) {
    const coupon = couponData.coupon;
    const orderAmount = this.data.totalAmount;

    // 检查金额条件
    if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
      return 0;
    }

    if (coupon.type === 'DISCOUNT') {
      return Math.min(coupon.quantity, orderAmount);
    } else if (coupon.type === 'PERCENTAGE') {
      return orderAmount * coupon.quantity;
    } else if (coupon.type === 'FREE_SHIPPING') {
      // 免运费券的处理逻辑
      return 0; // 实际应用中需要根据具体业务逻辑计算
    }

    return 0;
  }
});