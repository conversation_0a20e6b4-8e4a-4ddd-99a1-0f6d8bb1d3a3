.readlog-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background-color: #f5f7fa;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  padding: 50rpx 0;
  width: 100%;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.userinfo-avatar {
  width: 150rpx;
  height: 150rpx;
  margin: 20rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.userinfo-nickname {
  color: #333;
  font-size: 36rpx;
  margin-top: 10rpx;
}

.userinfo-phone {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.login-btn {
  margin-top: 30rpx;
  background-color: #fff;
  color: #07c160;
  font-size: 32rpx;
  padding: 15rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.login-btn:active {
  transform: scale(0.95);
}

.get-userinfo-btn,
.get-phone-btn {
  margin: 10rpx 0;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background: #07c160;
  color: #fff;
  border-radius: 30rpx;
}

.feedback-button {
  opacity: 0;
  position: absolute;
  width: 100vw;
  height: 50%;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

.contact-button {
  opacity: 0;
  position: absolute;
  width: 100vw;
  height: 50%;
  margin: 0;
  padding: 0;
  bottom: 0;
  left: 0;
}

.user-cell {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.07);
  overflow: hidden;
  width: 92%;
}

.account-info {
  padding: 40rpx;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  color: #333;
}

.account-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  font-size: 30rpx;
  color: #333;
}

.account-details-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 40rpx;
  font-size: 30rpx;
  color: #333;
}

.account-detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  margin-bottom: 30rpx;
}

.account-detail-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.account-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.recharge-button,
.withdraw-button {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.recharge-button {
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
}

.withdraw-button {
  background-color: #f8f8f8;
  color: #333;
  border: 1rpx solid #ddd;
}

.recharge-button:active,
.withdraw-button:active {
  transform: scale(0.95);
}

.withdraw-tip {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-top: 10rpx;
  text-align: center;
  display: block;
}

.withdraw-button[disabled] {
  background-color: #f0f0f0;
  color: #999;
  border: 1rpx solid #e0e0e0;
  cursor: not-allowed;
}

.menu-list {
  background-color: #fff;
  padding: 10rpx 0;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 36rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.menu-item:active {
  background-color: #f5f7fa;
}

.menu-text {
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  color: #bbb;
  font-size: 32rpx;
}

.logout {
  margin-top: 30rpx;
  border-top: 10rpx solid #f8f8f8;
}

.logout .menu-text {
  color: #ff4d4f;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 10rpx rgba(7, 193, 96, 0.2);
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.copyright {
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  text-align: center;
}

@media screen and (max-width: 375px) {
  .user-cell {
    margin: 20rpx;
  }

  .account-info,
  .menu-item {
    padding: 30rpx;
  }
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 20rpx 0;
}

.avatar-wrapper {
  background-color: transparent;
  padding: 0;
  width: 150rpx;
  height: 150rpx;
  margin: 20rpx 0;
  border-radius: 50%;
}

.avatar-wrapper::after {
  border: none;
}

.avatar-circle {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.nickname-input {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  font-size: 32rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  text-align: center;
  margin-top: 20rpx;
  width: 60%;
}

.subscribe-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.subscribe-text {
  font-size: 28rpx;
  color: #666;
  font-weight: normal;
}

.subscribe-switch {
  transform: scale(0.8);
}

/* 企业选择器样式 */
.enterprise-selector {
  flex: 1;
  margin-left: 20rpx;
}

.enterprise-picker {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  border: 1rpx solid #e9ecef;
}

.enterprise-name {
  font-size: 26rpx;
  color: #495057;
  max-width: 200rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.picker-arrow {
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #6c757d;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.single-enterprise {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  margin-left: 20rpx;
}

.single-enterprise .enterprise-name {
  font-size: 26rpx;
  color: #495057;
  max-width: 250rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 10rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
}

.offline-order-item {
  cursor: pointer;
  transition: background-color 0.3s;
}

.offline-order-item:active {
  background-color: #f5f7fa;
}

/* 弹窗样式 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  padding: 50rpx 30rpx 60rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
  animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
  to {
    transform: scale(1);
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 50rpx;
}

.dialog-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 48rpx;
  color: #999;
}

.meal-type-container {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
}

.meal-type-btn {
  flex: 1;
  background: #f8f9fa;
  border: none;
  padding: 40rpx 20rpx;
  border-radius: 16rpx;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: 180rpx;
}

.meal-type-btn::after {
  border: none;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
}

.meal-type-btn.active {
  background-color: #4080ff;
  color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 20rpx rgba(60, 179, 113, 0.3);
}

.offline-order-item {
  cursor: pointer;
  transition: background-color 0.3s;
}

.offline-order-item:active {
  background-color: #f5f7fa;
}

/* 离线点餐模块样式 */
.offline-order-container {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.offline-order-card {
  flex: 1;
  background: linear-gradient(135deg, #07c160, #05a351);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s ease;
}

.offline-order-card:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.3);
}

.offline-order-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  color: #fff;
}

.offline-order-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  margin-bottom: 6rpx;
}

.offline-order-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 禁用的离线点餐卡片样式 */
.offline-order-card.disabled {
  background: linear-gradient(135deg, #b3b3b3, #8c8c8c);
  position: relative;
  cursor: not-allowed;
  opacity: 0.8;
}

.offline-order-card.disabled:active {
  transform: none;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
}

.disabled-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  transform: rotate(5deg);
}

/* 统计按钮样式 */
.statistic-button-container {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.statistic-button {
  background: linear-gradient(135deg, #07c160, #05a351);
  border: none;
  border-radius: 15rpx;
  padding: 5rpx 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
  width: 100%;
}

.statistic-button::after {
  border: none;
}

.statistic-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 8rpx rgba(24, 144, 255, 0.3);
}

.statistic-button-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
  color: #fff;
}

.statistic-button-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #fff;
}

/* 二维码容器样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.qrcode-title {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

.qrcode-canvas {
  width: 200px;
  height: 200px;
  margin: 10px 0;
}

.qrcode-type {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0;
  color: #333;
}

.qrcode-back-btn {
  margin-top: 15px;
  background-color: #f2f2f2;
  color: #333;
  border: 1px solid #ddd;
  font-size: 14px;
  padding: 6px 15px;
  border-radius: 4px;
}
