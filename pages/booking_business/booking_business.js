// pages/booking_business/booking_business.js
import { loginRequest } from "../../service/index"
import { getUserInfo } from "../../service/user"

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    storeInfo: {},
    categories: [],
    dishes: [],
    currentCategory: 0,
    currentCategoryDishes: [],
    cartItems: [],
    cartTotal: {
      count: 0,
      price: 0
    },
    cartVisible: false,
    showDishDetail: false,
    selectedDish: {},
    showCheckout: false,
    contactPhone: '',
    orderRemark: '',
    bookingDate: '',
    bookingTime: '',
    peopleCount: '',
    contactName: '',
    orderSuccess: false,
    orderId: '',
    pageNo: 1,
    pageSize: 20,
    hasMore: true,
    isTestMode: false,
    dateTimeArray: [],
    dateTimeIndex: [],
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [], // 企业列表
    selectedEnterprise: null, // 选中的企业ID
    source: 'unknown',
    showAvailableSlotsDialog: false,
    availableSlots: [],
    dialogMessage: '',
    // 优惠券相关数据
    showCouponList: false,  // 是否显示优惠券列表
    selectedCoupons: [],     // 选中的优惠券列表（支持多选）
    selectedCouponIds: [],   // 选中的优惠券ID列表
    couponDiscount: 0,      // 优惠券总折扣金额
    finalAmount: 0,         // 最终金额（扣除优惠券后）
    showRecentNames: false,
    recentNamePhonePairs: [] // 改为姓名+电话对的数组
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 保存来源信息
    this.setData({
      source: options.source || 'unknown'
    });

    wx.setNavigationBarTitle({
      title: '商务餐'
    });

    // 获取门店ID，如果有的话
    const storeId = options.store_id || '1';

    // 获取预约配置和企业列表
    this.getReserveConfig();

    // 获取商家信息和分类
    this.fetchStoreInfo(storeId);
    this.fetchCategories(storeId);

    // 构建年月日时分的数组，并设置默认选中明天12点
    const date = new Date();
    const tomorrow = new Date(date);
    tomorrow.setDate(date.getDate() + 1); // 设置为明天
    
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 获取当前年、月、日（用于构建选项）
    const currentYear = date.getFullYear();
    const currentMonth = date.getMonth() + 1; // 月份从0开始
    const currentDay = date.getDate();
    
    // 获取明天的年、月、日（用于默认选中）
    const tomorrowYear = tomorrow.getFullYear();
    const tomorrowMonth = tomorrow.getMonth() + 1;
    const tomorrowDay = tomorrow.getDate();

    // 年份数组
    for (let i = currentYear; i <= currentYear + 5; i++) {
      years.push(i + "年");
    }

    // 月份数组
    for (let i = 1; i <= 12; i++) {
      months.push(i < 10 ? '0' + i + '月' : i + '月');
    }

    // 日期数组 - 简化处理，使用固定31天
    for (let i = 1; i <= 31; i++) {
      days.push(i < 10 ? '0' + i + '日' : i + '日');
    }

    // 限制时间选择范围为6-21点
    for (let i = 6; i <= 21; i++) {
      hours.push(i < 10 ? '0' + i + '时' : i + '时');
    }

    // 分钟数组，每30分钟一个选项
    for (let i = 0; i < 60; i += 30) {
      minutes.push(i < 10 ? '0' + i + '分' : i + '分');
    }

    // 计算默认选中的索引（明天12点）
    const yearIndex = tomorrowYear - currentYear; // 明天年份的索引
    const monthIndex = tomorrowMonth - 1; // 月份索引从0开始
    const dayIndex = tomorrowDay - 1; // 日期索引从0开始

    // 设置默认时间为12点（中午）
    const hourIndex = 12 - 6; // 12点对应索引6（hours数组从6点开始）
    const minuteIndex = 0; // 整点，对应0分钟

    // 设置默认选中的日期时间
    const dateTimeIndex = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex];

    // 根据选中的索引获取实际的日期时间值
    const selectedYear = years[dateTimeIndex[0]].replace('年', '');
    const selectedMonth = months[dateTimeIndex[1]].replace('月', '');
    const selectedDay = days[dateTimeIndex[2]].replace('日', '');
    const selectedHour = hours[dateTimeIndex[3]].replace('时', '');
    const selectedMinute = minutes[dateTimeIndex[4]].replace('分', '');

    // 格式化为显示用的字符串
    const bookingDate = `${selectedYear}年${selectedMonth}月${selectedDay}日`;
    const bookingTime = `${selectedHour}时${selectedMinute}分`;

    this.setData({
      dateTimeArray: [years, months, days, hours, minutes],
      dateTimeIndex: dateTimeIndex,
      bookingDate: bookingDate,
      bookingTime: bookingTime
    });

    // 加载最近使用的姓名+电话对，并直接显示标签
    this.loadRecentNames();
    this.setData({
      showRecentNames: true
    });
  },

  /**
   * 获取预约配置和企业列表
   */
  getReserveConfig() {
    wx.showLoading({
      title: '加载中...',
    });

    wx.request({
      url: `${getApp().globalData.baseUrl}/reserve/config`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        type: 'company_business',
        source: this.data.source || 'unknown'
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 静默保存企业列表，不显示提示
          this.setData({
            enterpriseList: res.data.data.enterprise_list || []
          });
        } else {
          // 只在失败时显示提示
          wx.showToast({
            title: res.data.message || '获取配置失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 获取商家信息
   */
  fetchStoreInfo(storeId) {
    loginRequest.get({
      url: '/store/detail',
      data: { id: storeId },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        this.setData({
          storeInfo: res.data || {
            name: '素食餐厅',
            address: '美丽的地球村',
            rating: '5.0',
            ratingCount: '999+',
            image: '/images/default-store.png'
          }
        });
      } else {
        wx.showToast({
          title: res.message || '获取商家信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取商家信息失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 获取菜单分类
   */
  fetchCategories(storeId) {
    loginRequest.get({
      url: '/menu/categories',
      data: { store_id: storeId },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        // 如果没有分类数据，创建一个默认分类
        const categories = res.data && res.data.length > 0 ? res.data : [{ id: '0', name: '所有菜品' }];

        this.setData({
          categories: categories,
          loading: false
        });

        // 获取第一个分类的菜品
        if (categories.length > 0) {
          this.fetchDishes(categories[0].id);
        }
      } else {
        this.setData({
          categories: [{ id: '0', name: '所有菜品' }],
          loading: false
        });

        // 获取默认分类的菜品
        this.fetchDishes('0');

        wx.showToast({
          title: res.message || '获取分类失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取分类失败', err);
      this.setData({
        categories: [{ id: '0', name: '所有菜品' }],
        loading: false
      });

      // 获取默认分类的菜品
      this.fetchDishes('0');

      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 获取菜品列表
   */
  fetchDishes(categoryId, append = false) {
    // 删除测试模式代码，始终使用API请求
    const { pageNo, pageSize } = this.data;

    this.setData({
      loading: !append // 只有在不是加载更多的情况下才显示loading
    });

    loginRequest.get({
      url: '/menu/dishes',
      data: {
        category_id: categoryId,
        page_no: pageNo,
        page_size: pageSize
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const newDishes = res.data.list || [];
        const hasMore = newDishes.length === pageSize;

        // 处理菜品数据，添加购物车中的数量
        newDishes.forEach(dish => {
          // 查找购物车中是否有该菜品
          const cartItem = this.data.cartItems.find(item => item.id === dish.id);
          dish.count = cartItem ? cartItem.count : 0;
        });

        if (append) {
          // 追加数据
          const updatedDishes = [...this.data.dishes, ...newDishes];
          const updatedCurrentDishes = [...this.data.currentCategoryDishes, ...newDishes];

          // 确保所有菜品的count值与购物车同步
          const syncedDishes = this.syncDishesWithCart(updatedDishes);
          const syncedCurrentDishes = this.syncDishesWithCart(updatedCurrentDishes);

          this.setData({
            dishes: syncedDishes,
            currentCategoryDishes: syncedCurrentDishes,
            pageNo: pageNo + 1,
            hasMore,
            loading: false
          });
        } else {
          // 替换当前分类菜品，但保留其他分类的菜品
          const currentDishes = this.data.dishes || [];

          // 移除当前分类的旧菜品
          const otherDishes = currentDishes.filter(dish => {
            // 检查这个菜品是否属于其他分类（不在newDishes中）
            return !newDishes.some(newDish => newDish.id === dish.id);
          });

          // 合并其他分类的菜品和新获取的当前分类菜品
          const updatedDishes = [...otherDishes, ...newDishes];

          // 确保所有菜品的count值与购物车同步
          const syncedDishes = this.syncDishesWithCart(updatedDishes);
          const syncedCurrentDishes = this.syncDishesWithCart(newDishes);

          this.setData({
            dishes: syncedDishes,
            currentCategoryDishes: syncedCurrentDishes,
            pageNo: 2, // 重置为第二页，因为第一页已经加载了
            hasMore,
            loading: false
          });
        }
      } else {
        this.setData({
          loading: false
        });

        if (!append) {
          this.setData({
            currentCategoryDishes: []
          });
        }

        wx.showToast({
          title: res.message || '获取菜品失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取菜品失败', err);
      this.setData({
        loading: false
      });

      if (!append) {
        this.setData({
          currentCategoryDishes: []
        });
      }

      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 同步菜品数量与购物车
   */
  syncDishesWithCart(dishes) {
    return dishes.map(dish => {
      // 查找购物车中是否有该菜品
      const cartItem = this.data.cartItems.find(item => item.id === dish.id);
      if (cartItem) {
        dish.count = cartItem.count;
      }
      return dish;
    });
  },

  /**
   * 切换分类
   */
  switchCategory(e) {
    const index = e.currentTarget.dataset.index;
    const categoryId = this.data.categories[index].id;

    this.setData({
      currentCategory: index,
      pageNo: 1 // 重置页码
    });

    // 删除测试模式代码，直接请求后端数据
    this.fetchDishes(categoryId);
  },

  /**
   * 加载更多菜品
   */
  loadMoreDishes() {
    // 删除测试模式代码
    if (!this.data.hasMore || this.data.loading) return;

    const categoryId = this.data.categories[this.data.currentCategory].id;
    this.fetchDishes(categoryId, true);
  },

  /**
   * 添加菜品到购物车
   */
  addDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1);
  },

  /**
   * 从购物车中减少菜品
   */
  minusDish(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1);
  },

  /**
   * 在弹窗中添加菜品
   */
  addDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, 1, true);
  },

  /**
   * 在弹窗中减少菜品
   */
  minusDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    this.updateCart(dishId, -1, true);
  },

  /**
   * 更新购物车
   */
  updateCart(dishId, change, isModal = false) {
    // 查找菜品
    const dish = this.data.dishes.find(item => item.id === dishId);
    if (!dish) return;

    // 更新菜品计数
    const oldCount = dish.count || 0;
    const newCount = Math.max(0, oldCount + change);
    dish.count = newCount;

    // 更新所有菜品列表中的计数
    const dishes = this.data.dishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    // 更新当前分类菜品列表中的计数
    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => {
      if (item.id === dishId) {
        item.count = newCount;
      }
      return item;
    });

    // 更新购物车
    let cartItems = [...this.data.cartItems];

    if (newCount > 0) {
      // 查找购物车中是否已有该菜品
      const cartItemIndex = cartItems.findIndex(item => item.id === dishId);

      if (cartItemIndex >= 0) {
        // 更新已有菜品的数量
        cartItems[cartItemIndex].count = newCount;
      } else {
        // 添加新菜品到购物车
        cartItems.push({
          id: dish.id,
          name: dish.name,
          price: dish.price,
          count: newCount
        });
      }
    } else {
      // 从购物车中移除该菜品
      cartItems = cartItems.filter(item => item.id !== dishId);
    }

    // 计算购物车总数和总价
    const cartTotal = this.calculateCartTotal(cartItems);

    // 重新计算最终金额（如果有选中的优惠券）
    let finalAmount = cartTotal.price;

    // 更新数据
    const updateData = {
      dishes,
      currentCategoryDishes,
      cartItems,
      cartTotal,
      finalAmount
    };

    // 如果是在弹窗中操作，同时更新选中的菜品
    if (isModal) {
      updateData.selectedDish = { ...dish };
    }

    this.setData(updateData);

    // 如果有选中的优惠券，重新计算优惠券价格
    if (this.data.selectedCoupons.length > 0) {
      const couponIds = this.data.selectedCouponIds;
      const coupons = this.data.selectedCoupons;

      // 延迟执行，确保购物车数据已更新
      setTimeout(() => {
        this.calculateCouponPricing(couponIds, coupons);
      }, 100);
    }
  },

  /**
   * 计算购物车总数和总价
   */
  calculateCartTotal(cartItems) {
    let count = 0;
    let price = 0;

    cartItems.forEach(item => {
      count += item.count;
      price += item.price * item.count;
    });

    return {
      count,
      price: parseFloat(price.toFixed(2))
    };
  },

  /**
   * 切换购物车展开/收起状态
   */
  toggleCart() {
    // 只有购物车有商品时才能展开
    if (this.data.cartTotal.count > 0) {
      this.setData({
        cartVisible: !this.data.cartVisible
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart() {
    // 重置所有菜品的计数
    const dishes = this.data.dishes.map(item => {
      item.count = 0;
      return item;
    });

    // 重置当前分类的菜品计数
    const currentCategoryDishes = this.data.currentCategoryDishes.map(item => {
      item.count = 0;
      return item;
    });

    // 清空购物车和优惠券
    this.setData({
      dishes,
      currentCategoryDishes,
      cartItems: [],
      cartTotal: {
        count: 0,
        price: 0
      },
      cartVisible: false,
      // 同时清空优惠券相关数据
      selectedCoupons: [],
      selectedCouponIds: [],
      couponDiscount: 0,
      finalAmount: 0
    });
  },

  /**
   * 显示菜品详情
   */
  showDishDetail(e) {
    const dish = e.currentTarget.dataset.dish;

    this.setData({
      showDishDetail: true,
      selectedDish: { ...dish }
    });
  },

  /**
   * 隐藏菜品详情
   */
  hideDishDetail() {
    this.setData({
      showDishDetail: false
    });
  },

  /**
   * 前往结算页面
   */
  goToCheckout() {
    if (this.data.cartTotal.count <= 0) return;

    // 自动填充用户手机号
    const app = getApp();
    let phone = '';
    let contact_name = '';

    if (app.globalData.userInfo && app.globalData.userInfo.phoneNumber && app.globalData.userInfo.real_name) {
      phone = app.globalData.userInfo.phoneNumber;
      contact_name = app.globalData.userInfo.real_name;
    } else {
      // 测试模式下提供默认手机号
      phone = this.data.isTestMode ? '13800138000' : '';
    }

    this.setData({
      showCheckout: true,
      contactPhone: phone,
      contactName: contact_name
    });
  },

  /**
   * 隐藏结算页面
   */
  hideCheckout() {
    this.setData({
      showCheckout: false
    });
  },

  /**
   * 输入预约日期
   */
  inputDate(e) {
    this.setData({
      bookingDate: e.detail.value
    });
  },

  /**
   * 输入预约时间
   */
  inputTime(e) {
    this.setData({
      bookingTime: e.detail.value
    });
  },

  /**
   * 输入人数
   */
  inputPeople(e) {
    this.setData({
      peopleCount: e.detail.value
    });
  },

  /**
   * 输入姓名
   */
  inputName(e) {
    this.setData({
      contactName: e.detail.value
    });
  },

  /**
   * 输入手机号
   */
  inputPhone(e) {
    this.setData({
      contactPhone: e.detail.value
    });
  },

  /**
   * 输入备注
   */
  inputRemark(e) {
    this.setData({
      orderRemark: e.detail.value
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    // 首先检查用户是否已登录
    const token = wx.getStorageSync('token');
    if (!token) {
      // 未登录，跳转到授权页面并传递回跳参数
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business'
      });
      return;
    }

    // 以下是原有的验证逻辑
    // 验证手机号
    const phone = this.data.contactPhone;
    const phoneRegex = /(^$)|^1[3-9]\d{9}$/;

    if (!phoneRegex.test(phone)) {
      wx.showToast({
        title: '请输入有效的手机号码',
        icon: 'none'
      });
      return;
    }

    // 验证必填项
    if (!this.data.bookingDate) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
      return;
    }

    if (!this.data.bookingTime) {
      wx.showToast({
        title: '请选择预约时间',
        icon: 'none'
      });
      return;
    }

    if (!this.data.peopleCount) {
      wx.showToast({
        title: '请输入人数',
        icon: 'none'
      });
      return;
    }

    // if (!this.data.contactName) {
    //   wx.showToast({
    //     title: '请输入姓名',
    //     icon: 'none'
    //   });
    //   return;
    // }

    // 准备订单数据
    const orderItems = this.data.cartItems.map(item => ({
      dish_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));

    const orderData = {
      store_id: this.data.storeInfo.id || '1',
      contact_phone: phone,
      contact_name: this.data.contactName,
      booking_date: this.data.bookingDate,
      booking_time: this.data.bookingTime,
      people_count: this.data.peopleCount,
      remark: this.data.orderRemark,
      total_amount: this.data.finalAmount || this.data.cartTotal.price,
      coupon_id: this.data.selectedCoupons.map(coupon => coupon.coupon_usage_record.id).join(','),
      coupon_discount: this.data.couponDiscount || 0,
      items: orderItems
    };

    // 显示加载中
    wx.showLoading({
      title: '提交中...',
    });

    // 添加日志
    console.log('提交订单数据:', orderData);

    loginRequest.post({
      url: '/biz-order/create',
      data: orderData,
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      // 确保无论如何都隐藏加载提示
      wx.hideLoading();

      // 添加调试日志，查看完整的响应数据
      console.log('订单创建响应:', JSON.stringify(res));

      // 检查返回的数据格式
      if (res.order_no) {  // 直接检查返回的数据是否包含订单号
        // 显示支付选择弹窗
        this.showPaymentOptions(res);
      } else if (res.status === 401) {
        // 未登录或token过期，跳转到授权页面
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=booking_business'
        });
      } else if (res.status === 400 && res.available_slots) {
        // 直接调用方法显示弹窗
        this.showAvailableSlotsDialog(res.message, res.available_slots);
      } else {
        // 处理其他错误情况
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('请求失败', err);
      wx.hideLoading();

      wx.showToast({
        title: err.message || '网络错误，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },

  /**
   * 显示支付选择弹窗
   */
  showPaymentOptions(orderData) {
    // 使用最终金额（扣除优惠券后的金额）
    const finalPayableAmount = this.data.finalAmount || orderData.payable_amount;
    
    // 判断用户余额是否足够支付最终金额
    const canUseBalance = orderData.user_balance >= finalPayableAmount;

    this.setData({
      showPaymentOptions: true,
      paymentOrderData: orderData,
      selectedPaymentMethod: 'wxpay', // 默认选择微信支付
      canUseBalance: canUseBalance,
      userBalance: orderData.user_balance,
      payableAmount: finalPayableAmount, // 使用扣除优惠券后的最终金额
      // 如果有企业列表，默认选择第一个企业
      selectedEnterprise: this.data.enterpriseList.length > 0 ? this.data.enterpriseList[0].id : null
    });
  },

  /**
   * 选择支付方式
   */
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method,
      // 如果切换到企业支付，默认选择第一个企业
      selectedEnterprise: method === 'biz_enterprise' && this.data.enterpriseList.length > 0
        ? this.data.enterpriseList[0].id
        : this.data.selectedEnterprise
    });
  },

  /**
   * 确认支付
   */
  confirmPayment() {
    const { selectedPaymentMethod, paymentOrderData, selectedEnterprise } = this.data;

    // 如果是企业支付，检查是否选择了企业
    if (selectedPaymentMethod === 'biz_enterprise' && !selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    wx.request({
      url: `${getApp().globalData.baseUrl}/order/pay/create`,
      method: 'POST',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        order_no: paymentOrderData.order_no,
        paymentMethod: selectedPaymentMethod,
        amount: this.data.payableAmount, // 使用页面中设置的金额（已扣除优惠券）
        enterprise_id: selectedPaymentMethod === 'biz_enterprise' ? selectedEnterprise : undefined
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 200) {
          if (selectedPaymentMethod === 'wxpay') {
            const payParams = res.data.payParams;

            // 显示支付提示
            wx.showLoading({
              title: '正在调起支付...',
              mask: true
            });

            // 调起微信支付
            wx.requestPayment({
              ...payParams,
              success: () => {
                wx.hideLoading();
                this.paymentSuccess();
              },
              fail: (err) => {
                wx.hideLoading();
                console.error('支付失败', err);

                // 区分用户取消和真正的支付错误
                if (err.errMsg && err.errMsg.includes('cancel')) {
                  wx.showToast({
                    title: '支付已取消',
                    icon: 'none',
                    duration: 1500
                  });
                } else {
                  wx.showToast({
                    title: '支付失败，请重试',
                    icon: 'none',
                    duration: 2000
                  });
                }
              },
              complete: () => {
                // 确保loading被隐藏
                wx.hideLoading();
              }
            });
          } else if (selectedPaymentMethod === 'balance') {
            this.balancePaymentSuccess();
          } else if (selectedPaymentMethod === 'biz_enterprise') {
            this.paymentSuccess();
          }
        } else {
          wx.showToast({
            title: res.data.message || '支付失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求失败', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 余额支付成功
   */
  balancePaymentSuccess() {
    // 先更新页面状态
    this.setData({
      showPaymentOptions: false,
      orderSuccess: true,
      orderId: this.data.paymentOrderData.order_no
    });

    // 保存姓名+电话对到最近使用列表
    this.saveRecentNamePhonePair(this.data.contactName, this.data.contactPhone);

    // 显示成功提示
    wx.showToast({
      title: '余额支付成功',
      icon: 'success',
      duration: 2000
    });

    // 最后刷新用户信息
    this.refreshUserInfoAndBookings();
  },

  /**
   * 微信支付成功处理
   */
  paymentSuccess() {
    // 先更新页面状态
    this.setData({
      showPaymentOptions: false,
      orderSuccess: true,
      orderId: this.data.paymentOrderData.order_no
    });

    // 保存姓名+电话对到最近使用列表
    this.saveRecentNamePhonePair(this.data.contactName, this.data.contactPhone);

    // 显示成功提示
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    // 最后刷新用户信息
    this.refreshUserInfoAndBookings();
  },

  /**
   * 刷新用户余额和预订列表
   */
  refreshUserInfoAndBookings() {
    const app = getApp();

    // 使用service/user.js中的getUserInfo方法刷新用户信息
    getUserInfo().then(userInfo => {
      // 更新全局用户信息
      if (app.globalData.userInfo) {
        app.globalData.userInfo.balance = userInfo.balance || 0;
      }

      // 更新页面数据
      this.setData({
        userBalance: userInfo.balance || 0
      });
    }).catch(err => {
      console.error('获取用户信息失败', err);
      // 即使获取用户信息失败，也不应该影响支付成功的流程
    });
  },

  /**
   * 关闭支付弹窗
   */
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });
  },

  /**
   * 跳转到订单详情
   */
  goToOrderDetail() {
    wx.switchTab({
      url: '/pages/reserve/reserve?tab=all'
    });
  },

  /**
   * 返回菜单页面
   */
  backToMenu() {
    this.setData({
      orderSuccess: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '乙禾素食餐厅 - 健康美味素食',
      path: '/pages/booking_business/booking_business'
    };
  },

  /**
   * 日期时间选择器改变事件
   */
  bindDateTimeChange(e) {
    const value = e.detail.value;
    const { dateTimeArray } = this.data;

    // 获取选择的年月日
    const year = dateTimeArray[0][value[0]].replace('年', '');
    const month = dateTimeArray[1][value[1]].replace('月', '');
    const day = dateTimeArray[2][value[2]].replace('日', '');

    // 获取选择的时分
    const hour = dateTimeArray[3][value[3]].replace('时', '');
    const minute = dateTimeArray[4][value[4]].replace('分', '');

    // 格式化为YYYY-MM-DD格式的日期
    const dateStr = `${year}年${month}月${day}日`;
    // 格式化为HH:MM格式的时间
    const timeStr = `${hour}时${minute}分`;

    this.setData({
      dateTimeIndex: value,
      bookingDate: dateStr,
      bookingTime: timeStr
    });

    console.log('预约日期:', dateStr);
    console.log('预约时间:', timeStr);
  },

  /**
   * 选择企业
   */
  selectEnterprise(e) {
    const id = e.currentTarget.dataset.id;
    console.log('选择企业:', id); // 添加调试日志
    this.setData({
      selectedEnterprise: id
    }, () => {
      console.log('更新后的selectedEnterprise:', this.data.selectedEnterprise); // 添加调试日志
    });
  },

  // 在booking_business.js文件中添加makePhoneCall函数
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: '***********',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
      }
    });
  },

  // 关闭可用时段弹窗
  closeAvailableSlotsDialog() {
    this.setData({
      showAvailableSlotsDialog: false
    });
  },

  // 添加一个新方法来显示弹窗
  showAvailableSlotsDialog: function(message, slots) {
    console.log('显示可用时段弹窗', message, slots);
    this.setData({
      showAvailableSlotsDialog: true,
      availableSlots: slots || [],
      dialogMessage: message || '该时段不提供商务餐服务，请参考以下可用时段'
    });

    // 确认弹窗状态
    setTimeout(() => {
      console.log('弹窗显示状态:', this.data.showAvailableSlotsDialog);
    }, 100);
  },

  /**
   * 加载最近使用的姓名+电话对
   */
  loadRecentNames() {
    try {
      const recentNamePhonePairs = wx.getStorageSync('recentNamePhonePairs') || [];
      this.setData({
        recentNamePhonePairs: recentNamePhonePairs.slice(0, 3) // 只显示最近3个
      });
    } catch (error) {
      console.error('加载最近姓名+电话对失败:', error);
    }
  },

  /**
   * 保存姓名+电话对到最近使用列表
   */
  saveRecentNamePhonePair(name, phone) {
    // 处理姓名和电话为空的情况
    const processedName = name && name.trim() !== '' ? name.trim() : '';
    const processedPhone = phone && phone.trim() !== '' ? phone.trim() : '';
    
    // 如果姓名和电话都为空，则不保存
    if (!processedName && !processedPhone) return;
    
    try {
      let recentNamePhonePairs = wx.getStorageSync('recentNamePhonePairs') || [];
      
      // 创建新的姓名+电话对
      const newPair = {
        name: processedName,
        phone: processedPhone
      };
      
      // 移除重复的姓名+电话对组合
      recentNamePhonePairs = recentNamePhonePairs.filter(item => 
        !(item.name === processedName && item.phone === processedPhone)
      );
      
      // 将新的姓名+电话对添加到开头
      recentNamePhonePairs.unshift(newPair);
      
      // 只保留最近3个不同的组合
      recentNamePhonePairs = recentNamePhonePairs.slice(0, 3);
      
      // 保存到本地存储
      wx.setStorageSync('recentNamePhonePairs', recentNamePhonePairs);
      
      // 更新页面数据，确保标签始终显示
      this.setData({
        recentNamePhonePairs: recentNamePhonePairs,
        showRecentNames: true
      });
    } catch (error) {
      console.error('保存最近姓名+电话对失败:', error);
    }
  },

  /**
   * 选择最近使用的姓名+电话对
   */
  selectRecentNamePhonePair(e) {
    const index = e.currentTarget.dataset.index;
    const pair = this.data.recentNamePhonePairs[index];

    if (pair) {
      this.setData({
        contactName: pair.name || '',
        contactPhone: pair.phone || ''
      });
    }
  },

  // 优惠券相关方法
  // 生成产品数量列表
  generateProductList() {
    const products = [];

    // 从cartItems中提取产品信息
    if (this.data.cartItems && this.data.cartItems.length > 0) {
      this.data.cartItems.forEach(item => {
        if (item.id && item.count > 0) {
          products.push({
            product_id: item.id,
            quantity: item.count
          });
        }
      });
    }

    return products;
  },

  /**
   * 显示优惠券列表
   */
  showCouponList() {
    // 检查是否有购物车商品
    if (!this.data.cartItems || this.data.cartItems.length === 0) {
      wx.showToast({
        title: '请先选择商品',
        icon: 'none'
      });
      return;
    }

    // 确保用户信息已加载
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    if (products.length === 0) {
      wx.showToast({
        title: '暂无可用产品信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCouponList: true,
      products: products  // 保存产品列表供组件使用
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 调用后端API计算准确的优惠金额
      this.calculateCouponPricing(couponIds, coupons);
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [], // 清空选中的优惠券ID列表
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  // 调用后端API计算优惠券价格
  calculateCouponPricing(couponIds, coupons) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    // 如果没有优惠券，直接重置状态
    if (!couponIds || couponIds.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });
      return;
    }

    const requestData = {
      user_id: userInfo.id,
      products: products,
      coupon_usage_record_ids: couponIds
    };

    wx.showLoading({
      title: '计算优惠中...'
    });

    wx.request({
      url: `${app.globalData.baseUrl}/coupon/coupon-pricing`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'token': wx.getStorageSync('token')
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();

        if (res.data && res.data.status === 200) {
          const data = res.data.data;
          const pricingResult = data.pricing_result;

          if (pricingResult && pricingResult.discount) {
            const totalDiscount = pricingResult.discount.total_discount || 0;
            const discountCoupons = pricingResult.discount.coupons || [];

            // 为每张优惠券添加具体的折扣金额
            const couponsWithDiscount = coupons.map(coupon => {
              const discountInfo = discountCoupons.find(dc =>
                dc.coupon_usage_record_id === coupon.coupon_usage_record_id
              );

              return {
                ...coupon,
                discountAmount: discountInfo ? discountInfo.discount_amount : 0
              };
            });

            // 修正总计金额计算：小计 - 优惠金额
      const finalAmount = Math.max(0, this.data.cartTotal.price - totalDiscount);

      this.setData({
              selectedCoupons: couponsWithDiscount,
              selectedCouponIds: couponIds,
        couponDiscount: totalDiscount,
        finalAmount: finalAmount,
        showCouponList: false
      });

      wx.showToast({
              title: `已选择${coupons.length}张优惠券，优惠¥${totalDiscount}`,
        icon: 'success'
      });
    } else {
            wx.showToast({
              title: '计算优惠失败',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: res.data?.message || '计算优惠失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('计算优惠券价格失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 取消单张优惠券
  cancelCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;
    console.log('取消优惠券:', couponId);

    if (!couponId) {
      return;
    }

    // 从已选择的优惠券中移除指定的优惠券
    const updatedCoupons = this.data.selectedCoupons.filter(coupon =>
      coupon.coupon_usage_record_id !== couponId
    );

    const updatedCouponIds = this.data.selectedCouponIds.filter(id =>
      id !== couponId.toString()
    );

    if (updatedCoupons.length === 0) {
      // 如果没有剩余优惠券，直接清空
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });

      wx.showToast({
        title: '已取消所有优惠券',
        icon: 'none'
      });
    } else {
      // 重新计算剩余优惠券的价格
      this.calculateCouponPricing(updatedCouponIds, updatedCoupons);

      wx.showToast({
        title: '已取消该优惠券',
        icon: 'none'
      });
    }
  },

  // 清空所有优惠券
  clearAllCoupons() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已选择的优惠券吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            selectedCoupons: [],
            selectedCouponIds: [],
            couponDiscount: 0,
            finalAmount: this.data.cartTotal.price
          });

          wx.showToast({
            title: '已清空所有优惠券',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 计算优惠券折扣金额
   */
  calculateCouponDiscount(couponData) {
    const coupon = couponData.coupon;
    const orderAmount = this.data.cartTotal.price;

    // 检查金额条件
    if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
      return 0;
    }

    if (coupon.type === 'DISCOUNT') {
      return Math.min(coupon.quantity, orderAmount);
    } else if (coupon.type === 'PERCENTAGE') {
      return orderAmount * coupon.quantity;
    } else if (coupon.type === 'FREE_SHIPPING') {
      // 免运费券的处理逻辑
      return 0; // 实际应用中需要根据具体业务逻辑计算
    }

    return 0;
  }
})