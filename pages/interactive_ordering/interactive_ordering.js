// pages/interactive_ordering/interactive_ordering.js
import { loginRequest } from "../../service/index"
import { getUserInfo } from "../../service/user"

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    mealType: '',
    timeout: '',
    redirected: false,
    isCheckingLogin: false,
    showMealSelector: false,
    selectedType: '',
    initialLoadComplete: false,
    // 用于显示配置信息的数据
    showConfigResult: false,
    configData: null,
    isLoadingConfig: false,
    // 后端推荐的时段信息
    recommendedTimeSlot: null,
    todayDate: null,
    // 新增：就餐人数相关
    diningPeople: 1, // 默认1人
    showPeopleInput: false, // 是否显示人数输入
    isProcessingPayment: false, // 是否正在处理支付
    // 新增：无餐弹窗控制
    showNoMealDialog: false,
    // 新增：已有预订记录标记
    hasExistingReservation: false,
    // 新增：未开餐标记
    isNotOpenYet: false,

    // 支付相关 - 清理后的版本
    showPaymentOptions: false, // 个人餐和企业餐小金额支付
    showCombinedPayment: false, // 企业餐大金额混合支付
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [], // 企业列表
    selectedEnterprise: null, // 选中的企业ID
    totalAmount: 0, // 总金额
    // 企业混合支付相关
    enterprisePaymentCompleted: false, // 企业支付是否完成
    personalPaymentAmount: 0,
    currentOrderNo: '',
    selectedPersonalPaymentMethod: 'balance', // 默认选择个人账户
    personalPaymentFixedAmount: 3.00, // 个人支付固定金额，从API获取
    openEnterpriseList: [], // 开放支付的企业列表
    // 优惠券相关数据
    showCouponList: false,  // 是否显示优惠券列表
    selectedCoupons: [],     // 选中的优惠券列表（支持多选）
    selectedCouponIds: [],   // 选中的优惠券ID列表
    couponDiscount: 0,      // 优惠券总折扣金额
    finalAmount: 0,         // 最终金额（扣除优惠券后）
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var mealType = '';
    var timeout = '';
    
    // 处理扫描二维码的情况
    if (options.q) {
      const q = decodeURIComponent(options.q);
      console.log('扫码进入，原始链接:', q);
      console.log('解码后的链接:', q);
      console.log('扫码时间:', options.scancode_time);
      
      // 提取 type 参数
      const typeMatch = q.match(/[?&]type=([^&]*)/);
      console.log('餐类型匹配结果:', typeMatch);
      if (typeMatch && typeMatch[1]) {
        mealType = typeMatch[1];
        console.log('提取到的餐类型:', mealType);
      } else {
        console.log('未能从链接中提取到餐类型');
      }
      
      // 提取 timeout 参数
      const timeoutMatch = q.match(/[?&]timeout=([^&]*)/);
      console.log('超时时间匹配结果:', timeoutMatch);
      if (timeoutMatch && timeoutMatch[1]) {
        timeout = timeoutMatch[1];
        console.log('提取到的超时时间:', timeout);
      } else {
        console.log('未能从链接中提取到超时时间');
      }
    } else {
      mealType = options.type || '';
      timeout = options.timeout || '';
    }
    
    if (!mealType) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
      return;
    }
    
    // 保存参数到data中
    this.setData({
      mealType: mealType,
      timeout: timeout
    });
    
    // 延迟一段时间，等待系统自动登录完成
    setTimeout(() => {
      // 标记初始加载开始
      this.setData({
        initialLoadComplete: true
      });
      
      // 获取app实例，尝试调用自动登录方法
      const app = getApp();
      if (app && app.monitor_token) {
        console.log('尝试自动登录');
        app.monitor_token().then(() => {
          console.log('自动登录完成，检查token');
          // 自动登录完成后，检查token
          this.checkLoginStatusAfterDelay(mealType);
        }).catch(err => {
          console.error('自动登录失败', err);
          // 自动登录失败，延迟后检查token
          this.checkLoginStatusAfterDelay(mealType);
        });
      } else {
        // 无法获取app实例或没有自动登录方法，直接延迟检查token
        this.checkLoginStatusAfterDelay(mealType);
      }
    }, 1000); // 等待1秒，给系统时间自动登录

    // 获取用户信息
    this.getUserInfo();
    
    // 获取个人支付固定金额
    this.getPersonalPaymentFixedAmount();

    // 获取开放企业列表
    this.getOpenEnterpriseList();
  },
  
  // 选择餐类型
  selectMealType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    });
    
    // 直接确认选择，不需要额外的确认按钮
    this.confirmMealType();
  },
  
  // 隐藏餐类型选择弹窗
  hideMealSelector() {
    this.setData({
      showMealSelector: false
    });
    // 隐藏弹窗后跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },
  
  // 确认选择 - 修改为调用API而不是跳转
  confirmMealType() {
    if (!this.data.selectedType) {
      wx.showToast({
        title: '请选择餐类型',
        icon: 'none'
      });
      return;
    }
    
    // 更新mealType并隐藏选择弹窗
    this.setData({
      mealType: this.data.selectedType,
      showMealSelector: false,
      isLoadingConfig: true
    });
    
    // 调用API获取配置信息
    this.requestOnsiteConfig(this.data.selectedType);
  },
  
  // 请求配置信息的方法
  async requestOnsiteConfig(type) {
    try {
      const token = wx.getStorageSync('token');
      if (!token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + type
        });
        return;
      }

      console.log(`请求配置信息，type: ${type}`);
      
      // 先获取配置信息
      const configResponse = await loginRequest.get({
        url: `/onsite/config?type=${type}&source=admin`,
        header: {
          token: token
        }
      });

      console.log('API响应:', configResponse);

      if (configResponse.code === 200) {
        // 成功获取配置
        const configData = configResponse.data;

        // 根据后端返回的状态判断显示内容
        const status = configData.status || 'unknown';
        let hasExistingReservation = false;
        let isNotOpenYet = false;
        
        if (status === 'existing_reservation') {
          hasExistingReservation = true;
        } else if (status === 'not_open_yet') {
          isNotOpenYet = true;
        } else if (status === 'no_meal_today') {
          // 今天没有餐次，也显示未开餐
          isNotOpenYet = true;
        }

        const totalAmount = this.calculateTotalAmount(configData.recommended_slot, this.data.diningPeople, type);
        
        // 设置配置数据
        this.setData({
          isLoadingConfig: false,
          showConfigResult: true,
          configData: configData,
          recommendedTimeSlot: configData.recommended_slot || null,
          todayDate: configData.today_date || null,
          showPeopleInput: (type === 'personal' && configData.recommended_slot),
          diningPeople: 1,
          totalAmount: totalAmount,
          finalAmount: totalAmount, // 初始化最终金额
          hasExistingReservation: hasExistingReservation,
          isNotOpenYet: isNotOpenYet
        });

        // 如果是企业餐，在设置推荐时段后获取并过滤企业列表
        if (type === 'employee') {
          const enterpriseList = await this.getFilteredEnterpriseList();
          this.setData({
            enterpriseList: enterpriseList
          });
        }

        if (configData.recommended_slot) {
          console.log('后端推荐时段:', configData.recommended_slot.time);
          console.log('推荐时段餐次类型:', configData.recommended_slot.rule_meal_type);
        } else {
          console.log('今天没有可用时段，状态:', status);
        }
      } else {
        throw new Error(configResponse.message || '获取配置失败');
      }
      
    } catch (error) {
      console.error('请求配置失败:', error);
      
      this.setData({
        isLoadingConfig: false
      });
      
      wx.showToast({
        title: error.message || '获取配置失败',
        icon: 'none'
      });
      
      // 失败后重新显示选择弹窗
      this.setData({
        showMealSelector: true
      });
    }
  },

  // 新增：获取并过滤企业列表
  async getFilteredEnterpriseList() {
    try {
      const token = wx.getStorageSync('token');
      const response = await loginRequest.get({
        url: '/reserve/config?type=company_business&source=admin',
        header: {
          token: token
        }
      });

      if (response.code === 200 && response.data.enterprise_list) {
        let enterpriseList = response.data.enterprise_list;
        console.log('原始企业列表:', enterpriseList.map(e => e.company_name));

        // 检查当前推荐时段是否为晚餐
        const isCurrentSlotDinner = this.checkIfCurrentSlotIsDinner();
        console.log('当前时段是否为晚餐:', isCurrentSlotDinner);

        // 如果是晚餐，过滤企业列表
        if (isCurrentSlotDinner) {
          const openEnterpriseNames = this.data.openEnterpriseList;
          console.log('开放企业名单:', openEnterpriseNames);

          enterpriseList = enterpriseList.filter(enterprise => {
            const isOpen = openEnterpriseNames.includes(enterprise.company_name);
            console.log(`企业 ${enterprise.company_name} 是否在开放名单中:`, isOpen);
            return isOpen;
          });

          console.log('晚餐过滤后企业列表:', enterpriseList.map(e => e.company_name));

          // 如果晚餐时没有可用企业，显示提示
          if (enterpriseList.length === 0) {
            wx.showToast({
              title: '晚餐暂不支持企业支付',
              icon: 'none',
              duration: 2000
            });
          }
        }

        return enterpriseList;
      }
      return [];
    } catch (error) {
      console.error('获取企业列表失败:', error);
      return [];
    }
  },

  // 修改原有的 getEnterpriseList 方法，简化为不过滤版本（用于向后兼容）
  async getEnterpriseList() {
    try {
      const token = wx.getStorageSync('token');
      const response = await loginRequest.get({
        url: '/reserve/config?type=company_business&source=admin',
        header: {
          token: token
        }
      });

      if (response.code === 200 && response.data.enterprise_list) {
        return response.data.enterprise_list;
      }
      return [];
    } catch (error) {
      console.error('获取企业列表失败:', error);
      return [];
    }
  },

  // 修改检查晚餐的方法，添加更多调试信息
  checkIfCurrentSlotIsDinner() {
    const recommendedTimeSlot = this.data.recommendedTimeSlot;

    console.log('检查是否为晚餐，推荐时段:', recommendedTimeSlot);

    if (!recommendedTimeSlot) {
      console.log('没有推荐时段，返回false');
      return false;
    }

    // 检查 rule_meal_type 字段
    if (recommendedTimeSlot.rule_meal_type) {
      console.log('时段餐次类型:', recommendedTimeSlot.rule_meal_type);
      const isDinner = recommendedTimeSlot.rule_meal_type === 'dinner';
      console.log('是否为晚餐:', isDinner);
      return isDinner;
    }

    // 如果没有 rule_meal_type 字段，使用时间判断作为兜底方案
    const timeStr = recommendedTimeSlot.time;
    if (timeStr) {
      console.log('使用时间判断，时间字符串:', timeStr);
      const startTimeMatch = timeStr.match(/(\d{1,2}):(\d{2})/);
      if (startTimeMatch) {
        const startHour = parseInt(startTimeMatch[1]);
        console.log('开始小时:', startHour);
        // 17点之后认为是晚餐
        const isDinner = startHour >= 17;
        console.log('基于时间判断是否为晚餐:', isDinner);
        return isDinner;
      }
    }

    console.log('无法判断餐次类型，返回false');
    return false;
  },

  // 新增：获取开放企业列表
  async getOpenEnterpriseList() {
    try {
      const response = await loginRequest.get({
        url: '/config/get_open_enterprise_list'
      });

      console.log('获取开放企业列表响应:', response);

      if (Array.isArray(response)) {
        this.setData({
          openEnterpriseList: response
        });
        console.log('开放企业列表:', response);
      }
    } catch (error) {
      console.error('获取开放企业列表失败:', error);
      // 使用空数组作为默认值，不影响主流程
      this.setData({
        openEnterpriseList: []
      });
    }
  },

  // 新增：计算总金额
  calculateTotalAmount(timeSlot, people, type) {
    if (!timeSlot) return 0;
    
    const quantity = type === 'personal' ? people : 1;
    return timeSlot.price * quantity;
  },

  // 新增：获取用户信息
  getUserInfo() {
    getUserInfo().then(userInfo => {
      this.setData({
        userBalance: userInfo.balance || 0
      });
    }).catch(err => {
      console.error('获取用户信息失败', err);
    });
  },
  
  // 返回到选择页面
  backToMealSelector() {
    this.setData({
      showConfigResult: false,
      showMealSelector: true,
      configData: null,
      selectedType: '',
      recommendedTimeSlot: null,
      todayDate: null,
      showPeopleInput: false,
      diningPeople: 1,
      totalAmount: 0,
      hasExistingReservation: false, // 新增：重置已有预订记录标记
      isNotOpenYet: false // 新增：重置未开餐标记
    });
  },
  
  // 修改：处理人数变化，同时更新总金额
  onPeopleChange(e) {
    const people = parseInt(e.detail.value) || 1;
    if (people < 1) {
      wx.showToast({
        title: '人数不能少于1人',
        icon: 'none'
      });
      return;
    }
    if (people > 10) {
      wx.showToast({
        title: '人数不能超过10人',
        icon: 'none'
      });
      return;
    }
    
    const totalAmount = this.calculateTotalAmount(this.data.recommendedTimeSlot, people, this.data.selectedType);

    // 重新计算最终金额（如果有选中的优惠券）
    let finalAmount = totalAmount;
    if (this.data.selectedCoupons && this.data.selectedCoupons.length > 0) {
      // 使用后端API重新计算优惠券价格
      const couponIds = this.data.selectedCouponIds;
      const coupons = this.data.selectedCoupons;

      // 延迟执行，确保订单数据已更新
      setTimeout(() => {
        this.calculateCouponPricing(couponIds, coupons);
      }, 100);
    }

    this.setData({
      diningPeople: people,
      totalAmount: totalAmount,
      finalAmount: finalAmount
    });
  },

  // 修改：人数增加，同时更新总金额
  increasePeople() {
    if (this.data.diningPeople >= 10) {
      wx.showToast({
        title: '人数不能超过10人',
        icon: 'none'
      });
      return;
    }
    
    const newPeople = this.data.diningPeople + 1;
    const totalAmount = this.calculateTotalAmount(this.data.recommendedTimeSlot, newPeople, this.data.selectedType);

    // 重新计算最终金额（如果有选中的优惠券）
    let finalAmount = totalAmount;
    if (this.data.selectedCoupons && this.data.selectedCoupons.length > 0) {
      // 使用后端API重新计算优惠券价格
      const couponIds = this.data.selectedCouponIds;
      const coupons = this.data.selectedCoupons;

      // 延迟执行，确保订单数据已更新
      setTimeout(() => {
        this.calculateCouponPricing(couponIds, coupons);
      }, 100);
    }

    this.setData({
      diningPeople: newPeople,
      totalAmount: totalAmount,
      finalAmount: finalAmount
    });
  },

  // 修改：人数减少，同时更新总金额
  decreasePeople() {
    if (this.data.diningPeople <= 1) {
      wx.showToast({
        title: '人数不能少于1人',
        icon: 'none'
      });
      return;
    }
    
    const newPeople = this.data.diningPeople - 1;
    const totalAmount = this.calculateTotalAmount(this.data.recommendedTimeSlot, newPeople, this.data.selectedType);

    // 重新计算最终金额（如果有选中的优惠券）
    let finalAmount = totalAmount;
    if (this.data.selectedCoupons && this.data.selectedCoupons.length > 0) {
      // 使用后端API重新计算优惠券价格
      const couponIds = this.data.selectedCouponIds;
      const coupons = this.data.selectedCoupons;

      // 延迟执行，确保订单数据已更新
      setTimeout(() => {
        this.calculateCouponPricing(couponIds, coupons);
      }, 100);
    }

    this.setData({
      diningPeople: newPeople,
      totalAmount: totalAmount,
      finalAmount: finalAmount
    });
  },
  
  // 修改：选择推荐时段并提交订单
  async selectRecommendedTimeSlot() {
    if (!this.data.recommendedTimeSlot) {
      wx.showToast({
        title: '没有推荐时段',
        icon: 'none'
      });
      return;
    }

    // 如果是个人餐，检查人数是否已设置
    if (this.data.selectedType === 'personal') {
      if (!this.data.diningPeople || this.data.diningPeople < 1) {
        wx.showToast({
          title: '请设置就餐人数',
          icon: 'none'
        });
        return;
      }
    }

    // 防止重复点击
    if (this.data.isProcessingPayment) {
      return;
    }

    this.setData({
      isProcessingPayment: true
    });

    try {
      // 先提交订单
      const orderData = await this.submitReservation();
      
      if (orderData && orderData.order_no) {
        // 订单提交成功，显示支付选择弹窗
        this.showPaymentOptions(orderData);
      } else {
        throw new Error('订单提交失败');
      }

    } catch (error) {
      console.error('提交订单失败:', error);
      
      wx.showToast({
        title: error.message || '订单提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isProcessingPayment: false
      });
    }
  },

  // 修改：提交预约订单
  async submitReservation() {
    const timeSlot = this.data.recommendedTimeSlot;
    const people = this.data.selectedType === 'personal' ? this.data.diningPeople : 1;
    
    console.log('准备提交订单:', {
      type: this.data.selectedType,
      timeSlot: timeSlot.time,
      people: people,
      price: timeSlot.price
    });

    // 获取今天的日期，格式化为 MM-DD
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${month}-${day}`;

    // 构建预约数据，参考 booking 页面的格式
    const bookingData = [{
      date: dateStr, // 添加日期字段
      people: people, // 添加人数字段
      timeSlot: timeSlot.time, // 添加时间段字段
      mealPrice: timeSlot.price || 0, // 添加餐点价格
      rule_id: timeSlot.rule_id,
      rule_item_id: timeSlot.rule_item_id,
      product_id: timeSlot.product_id,
      quantity: people,
      reservation_period: timeSlot.reservation_period
    }];

    wx.showLoading({
      title: '提交中...',
    });

    try {
      // 调用预约提交API
      const token = wx.getStorageSync('token');
      const response = await loginRequest.post({
        url: '/reserve/submit',
        header: {
          token: token
        },
        data: {
          bookings: bookingData,
          source: 'admin_onsite', // 添加 source 参数，标识这是临时点餐
          coupon_id: this.data.selectedCoupons.map(coupon => coupon.coupon_usage_record.id).join(','),
          coupon_discount: this.data.couponDiscount || 0
        }
      });

      wx.hideLoading();
      console.log('订单提交响应:', response);

      if (response.status === 200) {
        return response.reservation_result;
      } else {
        throw new Error(response.message || '订单提交失败');
      }
    } catch (error) {
      wx.hideLoading();
      throw error;
    }
  },

  // 修改：显示支付选择弹窗（接收订单数据）
  showPaymentOptions(orderData) {
    // 判断用户余额是否足够支付
    const canUseBalance = orderData.user_balance >= orderData.payable_amount;
    
    this.setData({
      paymentOrderData: orderData,
      canUseBalance: canUseBalance,
      userBalance: orderData.user_balance,
      payableAmount: orderData.payable_amount
    });

    // 根据餐类型和金额显示不同的弹窗
    if (this.data.selectedType === 'employee') {
      // 检查是否为晚餐且没有可用企业
      const isCurrentSlotDinner = this.checkIfCurrentSlotIsDinner();
      const availableEnterprises = this.data.enterpriseList;

      if (isCurrentSlotDinner && availableEnterprises.length === 0) {
        // 晚餐且没有可用企业，强制个人支付
        wx.showModal({
          title: '支付提示',
          content: '晚餐时段暂不支持企业支付，将使用个人支付方式',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            this.setData({
              showPaymentOptions: true,
              showCombinedPayment: false,
              selectedPaymentMethod: 'wxpay',
              currentOrderNo: orderData.order_no
            });
          }
        });
        return;
      }

      // 企业餐：判断是否需要混合支付
      if (orderData.payable_amount > this.data.personalPaymentFixedAmount) {
        // 总金额大于个人支付固定金额，使用合并支付弹窗
        // 新增：计算个人支付部分的余额是否足够
        const canUseBalanceForPersonal = orderData.user_balance >= this.data.personalPaymentFixedAmount;
        
        this.setData({
          showCombinedPayment: true,
          showPaymentOptions: false,
          personalPaymentAmount: this.data.personalPaymentFixedAmount,
          currentOrderNo: orderData.order_no,
          selectedEnterprise: availableEnterprises.length > 0 ? availableEnterprises[0].id : null,
          selectedPersonalPaymentMethod: 'balance', // 重置个人支付方式
          canUseBalanceForPersonal: canUseBalanceForPersonal // 新增：个人支付部分的余额判断
        });
      } else {
        // 总金额小于等于个人支付固定金额，个人直接支付
        this.setData({
          showPaymentOptions: true,
          showCombinedPayment: false,
          selectedPaymentMethod: 'wxpay', // 默认选择微信支付
          currentOrderNo: orderData.order_no
        });
      }
    } else {
      // 个人餐：显示支付方式选择
      this.setData({
        showPaymentOptions: true,
        showCombinedPayment: false,
        selectedPaymentMethod: 'wxpay', // 默认选择微信支付
        currentOrderNo: orderData.order_no
      });
    }
  },

  // 选择支付方式（个人餐和企业餐小金额使用）
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method
    });
  },

  // 关闭支付弹窗（个人餐使用）
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });
  },

  // 选择个人支付方式（合并支付中使用）
  selectPersonalPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPersonalPaymentMethod: method
    });
  },

  // 修改：选择企业方法，确保类型匹配
  selectEnterprise(e) {
    const id = e.currentTarget.dataset.id;
    // 确保类型一致性，尝试转换为数字类型（如果适用）
    const enterpriseId = isNaN(id) ? id : parseInt(id);
    console.log('选择企业:', enterpriseId); // 添加调试日志
    console.log('dataset.id类型:', typeof id, '值:', id);
    console.log('enterprise.id类型:', typeof this.data.enterpriseList[0]?.id, '值:', this.data.enterpriseList[0]?.id);
    console.log('selectedEnterprise类型:', typeof this.data.selectedEnterprise, '值:', this.data.selectedEnterprise);
    this.setData({
      selectedEnterprise: enterpriseId
    }, () => {
      console.log('更新后的selectedEnterprise:', this.data.selectedEnterprise); // 添加调试日志
    });
  },

  // 修改确认支付方法，优化混合支付流程
  async confirmPayment() {
    const { selectedPaymentMethod, selectedType, payableAmount } = this.data;

    if (selectedType === 'employee' && payableAmount <= this.data.personalPaymentFixedAmount) {
      // 企业餐但金额小于等于个人支付固定金额，个人直接支付
      await this.processPayment(this.data.paymentOrderData);
    } else {
      // 个人餐或其他情况的直接支付流程
      await this.processPayment(this.data.paymentOrderData);
    }
  },

  // 显示分次支付确认对话框
  showSplitPaymentConfirm(enterpriseAmount, personalAmount) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '混合支付确认',
        content: `将使用企业账户支付 ${enterpriseAmount} 元，您需要额外支付 ${personalAmount} 元。确认继续吗？`,
        confirmText: '确认支付',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  // 优化的分次支付处理
  async processSplitPaymentWithConfirm() {
    const { selectedEnterprise, payableAmount } = this.data;

    this.setData({
      isProcessingPayment: true
    });

    try {
      wx.showLoading({
        title: '企业支付中...',
      });

      const token = wx.getStorageSync('token');
      
      // 第一步：企业支付
      const response = await loginRequest.post({
        url: '/order/pay/split',
        header: {
          token: token
        },
        data: {
          order_no: this.data.paymentOrderData.order_no,
          enterprise_id: selectedEnterprise,
          amount: payableAmount
        }
      });

      wx.hideLoading();

      if (response.status === 200) {
        // 关闭企业选择弹窗
        this.setData({
          showEnterpriseSelector: false
        });
        
        // 企业支付成功，立即显示个人支付选择
        this.showPersonalPaymentOptions(response.remaining_amount, this.data.paymentOrderData.order_no);
        
        // 设置15分钟超时定时器
        this.setSplitPaymentTimer();
      } else {
        throw new Error(response.message || '企业支付失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('分次支付失败:', error);
      
      wx.showToast({
        title: error.message || '企业支付失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isProcessingPayment: false
      });
    }
  },

  // 设置分次支付超时定时器
  setSplitPaymentTimer() {
    // 清除之前的定时器
    if (this.splitPaymentTimer) {
      clearTimeout(this.splitPaymentTimer);
    }
    
    // 设置15分钟超时
    this.splitPaymentTimer = setTimeout(() => {
      this.handleSplitPaymentTimeout();
    }, 15 * 60 * 1000); // 15分钟
  },

  // 处理分次支付超时
  async handleSplitPaymentTimeout() {
    wx.showModal({
      title: '支付超时',
      content: '个人支付部分超时，企业支付金额将自动退回。请重新下单。',
      showCancel: false,
      confirmText: '知道了',
      success: async () => {
        // 调用取消分次支付API
        await this.cancelSplitPayment();
        
        // 返回到餐类型选择页面
        this.backToMealSelector();
      }
    });
  },

  // 取消分次支付
  async cancelSplitPayment() {
    try {
      const token = wx.getStorageSync('token');
      
      await loginRequest.post({
        url: '/order/pay/split/cancel',
        header: {
          token: token
        },
        data: {
          order_no: this.data.currentOrderNo
        }
      });
      
    } catch (error) {
      console.error('取消分次支付失败:', error);
    }
  },

  // 修改个人支付确认方法，添加取消选项
  async confirmPersonalPayment() {
    const { selectedPersonalPaymentMethod, personalPaymentAmount, currentOrderNo } = this.data;

    this.setData({
      isProcessingPayment: true
    });

    try {
      wx.showLoading({
        title: '个人支付中...',
      });

      const token = wx.getStorageSync('token');
      const response = await loginRequest.post({
        url: '/order/pay/complete',
        header: {
          token: token
        },
        data: {
          order_no: currentOrderNo,
          paymentMethod: selectedPersonalPaymentMethod
        }
      });

      wx.hideLoading();

      if (response.status === 200) {
        // 清除超时定时器
        if (this.splitPaymentTimer) {
          clearTimeout(this.splitPaymentTimer);
          this.splitPaymentTimer = null;
        }
        
        if (selectedPersonalPaymentMethod === 'wxpay' && response.payParams) {
          // 显示支付提示
          wx.showLoading({
            title: '正在调起支付...',
            mask: true
          });
          
          // 微信支付处理...
          wx.requestPayment({
            ...response.payParams,
            success: () => {
              wx.hideLoading();
              this.splitPaymentSuccess();
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('个人微信支付失败', err);
              
              // 区分用户取消和真正的支付错误
              if (err.errMsg && err.errMsg.includes('cancel')) {
                wx.showToast({
                  title: '支付已取消',
                  icon: 'none',
                  duration: 1500
                });
              } else {
                wx.showToast({
                  title: '个人支付失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            },
            complete: () => {
              // 确保loading被隐藏
              wx.hideLoading();
            }
          });
        } else {
          // 个人账户支付成功
          this.splitPaymentSuccess();
        }
      } else {
        throw new Error(response.message || '个人支付失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('个人支付失败:', error);
      
      wx.showToast({
        title: error.message || '个人支付失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isProcessingPayment: false
      });
    }
  },

  // 分次支付成功
  splitPaymentSuccess() {
    this.setData({
      showPersonalPaymentOptions: false
    });

    // 核销订单
    const orderNo = this.data.currentOrderNo || this.data.paymentOrderData?.order_no;
    if (orderNo) {
      this.verifyOrder(orderNo);
    }

    wx.showToast({
      title: '支付完成',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 刷新用户信息
        this.getUserInfo();
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 2000);
      }
    });
  },

  // 修改：处理支付
  async processPayment(orderData) {
    const { selectedPaymentMethod, selectedEnterprise, payableAmount } = this.data;

    wx.showLoading({
      title: '处理中...',
    });

    try {
      const token = wx.getStorageSync('token');
      const response = await loginRequest.post({
        url: '/order/pay/create',
        header: {
          token: token
        },
        data: {
          order_no: orderData.order_no,
          paymentMethod: selectedPaymentMethod,
          amount: payableAmount,
          enterprise_id: selectedPaymentMethod === 'biz_enterprise' ? selectedEnterprise : undefined
        }
      });

      wx.hideLoading();

      if (response.status === 200) {
        if (selectedPaymentMethod === 'wxpay') {
          const payParams = response.payParams;
          
          // 显示支付提示
          wx.showLoading({
            title: '正在调起支付...',
            mask: true
          });
          
          // 调起微信支付
          wx.requestPayment({
            ...payParams,
            success: () => {
              wx.hideLoading();
              this.paymentSuccess();
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('支付失败', err);
              
              // 区分用户取消和真正的支付错误
              if (err.errMsg && err.errMsg.includes('cancel')) {
                wx.showToast({
                  title: '支付已取消',
                  icon: 'none',
                  duration: 1500
                });
              } else {
                wx.showToast({
                  title: '支付失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            },
            complete: () => {
              // 确保loading被隐藏
              wx.hideLoading();
            }
          });
        } else if (selectedPaymentMethod === 'balance') {
          this.paymentSuccess();
        } else if (selectedPaymentMethod === 'biz_enterprise') {
          this.paymentSuccess();
        }
      } else {
        throw new Error(response.message || '支付失败');
      }
    } catch (error) {
      wx.hideLoading();
      throw error;
    }
  },

  // 新增：支付成功处理
  paymentSuccess() {
    this.setData({
      showPaymentOptions: false
    });

    // 核销订单
    const orderNo = this.data.currentOrderNo || this.data.paymentOrderData?.order_no;
    if (orderNo) {
      this.verifyOrder(orderNo);
    }

    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 刷新用户信息
        this.getUserInfo();
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 2000);
      }
    });
  },

  // 处理时段选择
  onTimeSlotSelect(e) {
    const slotIndex = e.currentTarget.dataset.index;
    
    if (!this.data.configData?.dateList?.[0]?.timeSlots?.[slotIndex]) {
      return;
    }
    
    const selectedSlot = this.data.configData.dateList[0].timeSlots[slotIndex];
    
    if (!selectedSlot.can_select || selectedSlot.disabled || selectedSlot.available_capacity <= 0) {
      wx.showToast({
        title: '该时段不可选择',
        icon: 'none'
      });
      return;
    }

    const totalAmount = this.calculateTotalAmount(selectedSlot, this.data.diningPeople, this.data.selectedType);
    
    // 更新推荐时段
    this.setData({
      recommendedTimeSlot: selectedSlot,
      // 根据餐类型决定是否显示人数输入
      showPeopleInput: (this.data.selectedType === 'personal'),
      totalAmount: totalAmount,
      finalAmount: totalAmount // 重置最终金额
    });
    
    wx.showToast({
      title: `已选择 ${selectedSlot.time}`,
      icon: 'success'
    });
  },

  // 延迟检查登录状态
  checkLoginStatusAfterDelay(mealType) {
    console.log('延迟检查登录状态');
    // 再次延迟检查，确保token已经保存到storage
    setTimeout(() => {
      const token = wx.getStorageSync('token');
      console.log('获取到token:', token ? '有' : '无');
      
      if (token) {
        // 有token，检查是否有效
        this.checkLoginStatus(mealType);
      } else {
        // 仍然没有token，跳转到登录页
        console.log('延迟后仍未获取到token，跳转到登录页');
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
        });
      }
    }, 500); // 再等待0.5秒
  },
  
  // 检查登录状态
  checkLoginStatus(mealType) {
    // 避免重复检查
    if (this.data.isCheckingLogin) {
      console.log('正在检查登录状态，跳过重复检查');
      return;
    }
    
    this.setData({
      isCheckingLogin: true
    });
    
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未登录，跳转到登录页');
      this.setData({
        isCheckingLogin: false
      });
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
      });
      return;
    }
    
    console.log('开始验证token:', token);
    
    // 已有token，验证token是否有效
    loginRequest.post({
      url: '/auth',
      header: {
        token
      }
    }).then(res => {
      console.log('token验证结果:', res);
      
      if (res.message === "已登录") {
        console.log('token有效，显示选择页面');
        
        // 先更新状态，表示检查完成
        this.setData({
          isCheckingLogin: false
        }, () => {
          // 显示选择页面而不是跳转
          this.showMealSelectorPage(mealType);
        });
      } else {
        console.log('token无效，需要重新登录');
        // token无效，调用app的monitor_token方法重新登录
        const app = getApp();
        if (app && app.monitor_token) {
          app.monitor_token().then(() => {
            // 重新登录成功后，检查是否获取到新token
            const newToken = wx.getStorageSync('token');
            if (newToken) {
              // 显示选择页面
              this.showMealSelectorPage(mealType);
            } else {
              // 仍然没有token，跳转到登录页
              this.setData({
                isCheckingLogin: false
              });
              wx.navigateTo({
                url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
              });
            }
          }).catch(err => {
            console.error('重新登录失败', err);
            // 登录失败，跳转到登录页
            this.setData({
              isCheckingLogin: false
            });
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
            });
          });
        } else {
          // 无法获取app实例，直接跳转到登录页
          this.setData({
            isCheckingLogin: false
          });
          wx.navigateTo({
            url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
          });
        }
      }
    }).catch(err => {
      console.error('验证token失败', err);
      // 验证失败，跳转到登录页
      this.setData({
        isCheckingLogin: false
      });
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=interactive_ordering&type=' + mealType
      });
    });
  },
  
  // 修改：显示选择页面而不是跳转
  showMealSelectorPage(mealType) {
    // 如果是选择类型，显示选择弹窗
    if (mealType === 'select') {
      this.setData({
        loading: false,
        showMealSelector: true
      });
      return;
    }
    
    // 如果已经指定了类型，直接请求配置
    if (mealType === 'personal' || mealType === 'employee') {
      this.setData({
        selectedType: mealType,
        loading: false,
        isLoadingConfig: true
      });
      this.requestOnsiteConfig(mealType);
    } else {
      wx.showToast({
        title: '未知餐类型',
        icon: 'none'
      });
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
    }
  },

  // 新增：隐藏无餐弹窗
  hideNoMealDialog() {
    this.setData({
      showNoMealDialog: false
    });
  },

  // 修改：确认无餐，跳转到首页
  confirmNoMeal() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果页面被显示，但已经重定向过，说明用户点击了返回按钮
    // 这时应该跳转到首页，避免用户看到"正在跳转..."的提示
    if (this.data.redirected) {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }
    
    // 只有在初始加载完成后，才允许onShow进行登录检查
    if (!this.data.initialLoadComplete) {
      return;
    }
    
    // 页面显示时，如果不是正在检查登录状态，则检查登录状态
    if (!this.data.isCheckingLogin) {
      const mealType = this.data.mealType;
      if (mealType) {
        const token = wx.getStorageSync('token');
        if (token) {
          // 如果有token，尝试验证并显示选择页面
          this.checkLoginStatus(mealType);
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 显示个人支付选择
  showPersonalPaymentOptions(amount, orderNo) {
    this.setData({
      showPaymentOptions: false,
      showPersonalPaymentOptions: true,
      personalPaymentAmount: amount,
      currentOrderNo: orderNo,
      selectedPersonalPaymentMethod: 'balance' // 默认选择个人账户
    });
  },

  // 选择个人支付方式
  selectPersonalPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPersonalPaymentMethod: method
    });
  },

  // 添加关闭个人支付弹窗的确认
  closePersonalPayment() {
    wx.showModal({
      title: '确认取消',
      content: '取消个人支付将退回企业支付金额，确认取消吗？',
      confirmText: '确认取消',
      cancelText: '继续支付',
      success: async (res) => {
        if (res.confirm) {
          // 取消分次支付
          await this.cancelSplitPayment();
          
          this.setData({
            showPersonalPaymentOptions: false,
            showPaymentOptions: false,
            showEnterpriseSelector: false
          });
          
          // 返回到餐类型选择页面
          this.backToMealSelector();
        }
      }
    });
  },

  // 修改：企业支付处理
  async processEnterprisePayment() {
    const { selectedEnterprise, payableAmount, personalPaymentAmount } = this.data;

    if (!selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    const enterpriseAmount = payableAmount - personalPaymentAmount;
    
    // 显示确认对话框
    const result = await this.showSplitPaymentConfirm(enterpriseAmount, personalPaymentAmount);
    if (!result) {
      return; // 用户取消
    }

    this.setData({
      isProcessingPayment: true
    });

    try {
      wx.showLoading({
        title: '企业支付中...',
      });

      const token = wx.getStorageSync('token');
      
      // 第一步：企业支付
      const response = await loginRequest.post({
        url: '/order/pay/split',
        header: {
          token: token
        },
        data: {
          order_no: this.data.paymentOrderData.order_no,
          enterprise_id: selectedEnterprise,
          amount: payableAmount
        }
      });

      wx.hideLoading();

      if (response.status === 200) {
        // 企业支付成功，更新状态但不切换弹窗
        this.setData({
          enterprisePaymentCompleted: true
        });
        
        // 设置15分钟超时定时器
        this.setSplitPaymentTimer();
        
        wx.showToast({
          title: '企业支付完成',
          icon: 'success',
          duration: 1500
        });
      } else {
        throw new Error(response.message || '企业支付失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('企业支付失败:', error);
      
      wx.showToast({
        title: error.message || '企业支付失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isProcessingPayment: false
      });
    }
  },

  // 修改：个人支付确认方法
  async confirmPersonalPaymentInCombined() {
    const { selectedPersonalPaymentMethod, personalPaymentAmount, currentOrderNo } = this.data;

    this.setData({
      isProcessingPayment: true
    });

    try {
      wx.showLoading({
        title: '个人支付中...',
      });

      const token = wx.getStorageSync('token');
      const response = await loginRequest.post({
        url: '/order/pay/complete',
        header: {
          token: token
        },
        data: {
          order_no: currentOrderNo,
          paymentMethod: selectedPersonalPaymentMethod
        }
      });

      wx.hideLoading();

      if (response.status === 200) {
        // 清除超时定时器
        if (this.splitPaymentTimer) {
          clearTimeout(this.splitPaymentTimer);
          this.splitPaymentTimer = null;
        }
        
        if (selectedPersonalPaymentMethod === 'wxpay' && response.payParams) {
          // 显示支付提示
          wx.showLoading({
            title: '正在调起支付...',
            mask: true
          });
          
          // 微信支付处理...
          wx.requestPayment({
            ...response.payParams,
            success: () => {
              wx.hideLoading();
              this.combinedPaymentSuccess();
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('个人微信支付失败', err);
              
              // 区分用户取消和真正的支付错误
              if (err.errMsg && err.errMsg.includes('cancel')) {
                wx.showToast({
                  title: '支付已取消',
                  icon: 'none',
                  duration: 1500
                });
              } else {
                wx.showToast({
                  title: '个人支付失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            },
            complete: () => {
              // 确保loading被隐藏
              wx.hideLoading();
            }
          });
        } else {
          // 个人账户支付成功
          this.combinedPaymentSuccess();
        }
      } else {
        throw new Error(response.message || '个人支付失败');
      }

    } catch (error) {
      wx.hideLoading();
      console.error('个人支付失败:', error);
      
      wx.showToast({
        title: error.message || '个人支付失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        isProcessingPayment: false
      });
    }
  },

  // 合并支付成功
  combinedPaymentSuccess() {
    this.setData({
      showCombinedPayment: false
    });

    // 核销订单
    const orderNo = this.data.currentOrderNo || this.data.paymentOrderData?.order_no;
    if (orderNo) {
      this.verifyOrder(orderNo);
    }

    wx.showToast({
      title: '支付完成',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 刷新用户信息
        this.getUserInfo();
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 2000);
      }
    });
  },

  // 修改：关闭合并支付弹窗（简化版本，因为现在是一次性支付）
  closeCombinedPayment() {
    this.setData({
      showCombinedPayment: false
    });
  },

  // 新增：一次性确认混合支付
  async confirmCombinedPayment() {
    const { selectedEnterprise, selectedPersonalPaymentMethod, payableAmount, personalPaymentAmount } = this.data;

    if (!selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    if (!selectedPersonalPaymentMethod) {
      wx.showToast({
        title: '请选择个人支付方式',
        icon: 'none'
      });
      return;
    }

    // 显示支付确认
    const enterpriseAmount = payableAmount - personalPaymentAmount;
    try {
      const result = await this.showCombinedPaymentConfirm(enterpriseAmount, personalPaymentAmount);
      if (!result) {
        return; // 用户取消
      }
    } catch (error) {
      return; // 确认弹窗出错
    }

    this.setData({
      isProcessingPayment: true
    });

    let enterprisePaymentSuccess = false;
    let enterprisePaymentData = null;

    try {
      // 第一步：企业支付
      wx.showLoading({
        title: '企业支付中...',
      });

      const token = wx.getStorageSync('token');
      
      const enterpriseResponse = await loginRequest.post({
        url: '/order/pay/split',
        header: {
          token: token
        },
        data: {
          order_no: this.data.paymentOrderData.order_no,
          enterprise_id: selectedEnterprise,
          amount: payableAmount
        }
      });

      if (enterpriseResponse.status !== 200) {
        throw new Error(enterpriseResponse.message || '企业支付失败');
      }

      enterprisePaymentSuccess = true;
      enterprisePaymentData = enterpriseResponse;
      
      wx.hideLoading();
      
      // 第二步：个人支付
      wx.showLoading({
        title: '个人支付中...',
      });

      const personalResponse = await loginRequest.post({
        url: '/order/pay/complete',
        header: {
          token: token
        },
        data: {
          order_no: this.data.paymentOrderData.order_no,
          paymentMethod: selectedPersonalPaymentMethod
        }
      });

      if (personalResponse.status !== 200) {
        throw new Error(personalResponse.message || '个人支付失败');
      }

      // 处理微信支付
      if (selectedPersonalPaymentMethod === 'wxpay' && personalResponse.payParams) {
        wx.hideLoading();
        
        // 显示支付提示
        wx.showLoading({
          title: '正在调起支付...',
          mask: true
        });
        
        // 使用Promise包装微信支付，便于错误处理
        const wxPayResult = await new Promise((resolve, reject) => {
          wx.requestPayment({
            ...personalResponse.payParams,
            success: () => {
              resolve(true);
            },
            fail: (err) => {
              reject(err);
            }
          });
        });

        wx.hideLoading();
        
        // 微信支付成功
        this.combinedPaymentSuccess();
      } else {
        // 个人账户支付成功
        wx.hideLoading();
        this.combinedPaymentSuccess();
      }

    } catch (error) {
      wx.hideLoading();
      console.error('混合支付失败:', error);
      
      // 如果企业支付成功但个人支付失败，需要回滚企业支付
      if (enterprisePaymentSuccess) {
        wx.showLoading({
          title: '正在退回企业支付...',
        });
        
        // 确保currentOrderNo设置正确
        this.setData({
          currentOrderNo: this.data.paymentOrderData.order_no
        });
        
        try {
          await this.cancelSplitPayment();
          wx.hideLoading();
          
          // 显示回滚成功的提示
          wx.showModal({
            title: '支付失败',
            content: `个人支付失败：${error.message || '支付过程中出现问题'}。企业支付金额已自动退回，请重新尝试支付。`,
            showCancel: false,
            confirmText: '确定',
            success: () => {
              // 重置支付状态但保持弹窗开启，让用户重新选择
              this.setData({
                isProcessingPayment: false
              });
            }
          });
        } catch (rollbackError) {
          wx.hideLoading();
          console.error('回滚企业支付失败:', rollbackError);
          
          // 回滚失败的严重错误提示
          wx.showModal({
            title: '支付异常',
            content: `个人支付失败且企业支付无法自动退回。请联系客服处理，订单号：${this.data.paymentOrderData.order_no}`,
            showCancel: false,
            confirmText: '联系客服',
            success: () => {
              this.setData({
                isProcessingPayment: false,
                showCombinedPayment: false
              });
              // 可以在这里添加跳转到客服页面的逻辑
            }
          });
        }
      } else {
        // 企业支付就失败了
        wx.showModal({
          title: '支付失败',
          content: error.message || '企业支付失败，请重试',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            this.setData({
              isProcessingPayment: false
            });
          }
        });
      }
    }
  },

  // 新增：显示混合支付确认对话框
  showCombinedPaymentConfirm(enterpriseAmount, personalAmount) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '混合支付确认',
        content: `将使用企业账户支付 ${enterpriseAmount} 元，您需要额外支付 ${personalAmount} 元。确认继续吗？`,
        confirmText: '确认支付',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  // 新增：核销订单
  async verifyOrder(orderNo) {
    try {
      const token = wx.getStorageSync('token');
      const response = await loginRequest.post({
        url: '/onsite/verify',
        header: {
          token: token
        },
        data: {
          order_no: orderNo
        }
      });

      console.log('订单核销响应:', response);

      if (response.status === 200) {
        console.log('订单核销成功:', orderNo);
      } else {
        console.error('订单核销失败:', response.message);
        // 核销失败不影响用户体验，只记录日志
      }
    } catch (error) {
      console.error('订单核销请求失败:', error);
      // 核销失败不影响用户体验，只记录日志
    }
  },

  // 新增：获取个人支付固定金额
  async getPersonalPaymentFixedAmount() {
    try {
      const response = await loginRequest.get({
        url: '/config/get_personal_payment_fixed_amount'
      });

      if (response && typeof response === 'number') {
        this.setData({
          personalPaymentFixedAmount: response
        });
        console.log('获取个人支付固定金额成功:', response);
      } else {
        console.warn('获取个人支付固定金额失败，使用默认值3.00');
      }
    } catch (error) {
      console.error('获取个人支付固定金额失败:', error);
      // 使用默认值，不影响主流程
    }
  },

  // 优惠券相关方法
  // 生成产品数量列表
  generateProductList() {
    const products = [];

    // 从推荐时段中提取产品信息
    if (this.data.recommendedTimeSlot && this.data.recommendedTimeSlot.products) {
      this.data.recommendedTimeSlot.products.forEach(product => {
        if (product.product_id) {
          // 个人餐使用就餐人数，企业餐默认为1
          const quantity = this.data.selectedType === 'personal' ? this.data.diningPeople : 1;
          products.push({
            product_id: product.product_id,
            quantity: quantity
          });
        }
      });
    }

    return products;
  },

  /**
   * 显示优惠券列表
   */
  async showCouponList() {
    // 检查是否有推荐时段
    if (!this.data.recommendedTimeSlot) {
      wx.showToast({
        title: '请先选择时段',
        icon: 'none'
      });
      return;
    }

    // 确保用户信息已加载
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.id) {
      // 尝试重新获取用户信息
      try {
        wx.showLoading({
          title: '获取用户信息...'
        });

        const userInfo = await getUserInfo();
        wx.hideLoading();

        // 用户信息获取成功，继续显示优惠券列表
        console.log('重新获取用户信息成功:', userInfo);
      } catch (error) {
        wx.hideLoading();
        console.error('获取用户信息失败:', error);
        wx.showToast({
          title: '用户信息获取失败，请重新登录',
          icon: 'none'
        });
        return;
      }
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    if (products.length === 0) {
      wx.showToast({
        title: '暂无可用产品信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCouponList: true,
      products: products  // 保存产品列表供组件使用
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 调用后端API计算准确的优惠金额
      this.calculateCouponPricing(couponIds, coupons);
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [], // 清空选中的优惠券ID列表
        couponDiscount: 0,
        finalAmount: this.data.totalAmount,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  /**
   * 计算优惠券折扣金额
   */
  calculateCouponDiscount(couponData) {
    const coupon = couponData.coupon;
    const orderAmount = this.data.totalAmount;

    // 检查金额条件
    if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
      return 0;
    }

    if (coupon.type === 'DISCOUNT') {
      return Math.min(coupon.quantity, orderAmount);
    } else if (coupon.type === 'PERCENTAGE') {
      return orderAmount * coupon.quantity;
    } else if (coupon.type === 'FREE_SHIPPING') {
      // 免运费券的处理逻辑
      return 0; // 实际应用中需要根据具体业务逻辑计算
    }

    return 0;
  },

  /**
   * 调用后端API计算优惠券价格
   */
  calculateCouponPricing(couponIds, coupons) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    // 如果没有优惠券，直接重置状态
    if (!couponIds || couponIds.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.totalAmount
      });
      return;
    }

    const requestData = {
      user_id: userInfo.id,
      products: products,
      coupon_usage_record_ids: couponIds
    };

    wx.showLoading({
      title: '计算优惠中...'
    });

    wx.request({
      url: `${app.globalData.baseUrl}/coupon/coupon-pricing`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'token': wx.getStorageSync('token')
      },
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.code === 200) {
          const data = res.data.data;
          const pricingResult = data.pricing_result;

          if (pricingResult && pricingResult.discount) {
            const totalDiscount = pricingResult.discount.total_discount || 0;
            const discountCoupons = pricingResult.discount.coupons || [];

            // 为每张优惠券添加具体的折扣金额
            const couponsWithDiscount = coupons.map(coupon => {
              const discountInfo = discountCoupons.find(dc =>
                dc.coupon_usage_record_id === coupon.coupon_usage_record_id
              );

              return {
                ...coupon,
                discountAmount: discountInfo ? discountInfo.discount_amount : 0,
                // 确保保留 displayText，如果没有则根据优惠券类型生成
                displayText: coupon.displayText || this.generateDisplayText(coupon),
                // 确保有 uniqueId 用于 wx:key
                uniqueId: coupon.uniqueId || coupon.coupon_usage_record_id || coupon.coupon_usage_record?.id
              };
            });

            // 修正总计金额计算：小计 - 优惠金额
            const finalAmount = Math.max(0, this.data.totalAmount - totalDiscount);

            this.setData({
              selectedCoupons: couponsWithDiscount,
              selectedCouponIds: couponIds,
              couponDiscount: totalDiscount,
              finalAmount: finalAmount,
              showCouponList: false
            });

            wx.showToast({
              title: `已选择${coupons.length}张优惠券，优惠¥${totalDiscount}`,
              icon: 'success'
            });
          } else {
            wx.showToast({
              title: '计算优惠失败',
              icon: 'none'
            });
          }
        } else {
          wx.showToast({
            title: res.data.message || '计算优惠失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('计算优惠券价格失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 取消单张优惠券
   */
  cancelCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;
    console.log('取消优惠券:', couponId);

    if (!couponId) {
      return;
    }

    // 从已选择的优惠券中移除指定的优惠券
    const updatedCoupons = this.data.selectedCoupons.filter(coupon =>
      coupon.coupon_usage_record_id !== couponId
    );

    const updatedCouponIds = this.data.selectedCouponIds.filter(id =>
      id !== couponId.toString()
    );

    if (updatedCoupons.length === 0) {
      // 如果没有剩余优惠券，直接清空
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.totalAmount
      });

      wx.showToast({
        title: '已取消所有优惠券',
        icon: 'none'
      });
    } else {
      // 重新计算剩余优惠券的价格
      this.calculateCouponPricing(updatedCouponIds, updatedCoupons);

      wx.showToast({
        title: '已取消该优惠券',
        icon: 'none'
      });
    }
  },

  /**
   * 清空所有优惠券
   */
  clearAllCoupons() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已选择的优惠券吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            selectedCoupons: [],
            selectedCouponIds: [],
            couponDiscount: 0,
            finalAmount: this.data.totalAmount
          });

          wx.showToast({
            title: '已清空所有优惠券',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 生成优惠券显示文本
   */
  generateDisplayText(couponData) {
    const couponType = couponData.coupon_type || (couponData.coupon && couponData.coupon.type);

    if (couponType === 'cash') {
      return '现金券';
    } else if (couponType === 'full_reduction') {
      return '满减券';
    } else if (couponType === 'discount') {
      return '折扣券';
    } else {
      return '优惠券';
    }
  }

})