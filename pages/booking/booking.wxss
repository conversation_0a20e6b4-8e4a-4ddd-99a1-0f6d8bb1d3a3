/* 整体容器 */
.booking-container {
  padding: 20rpx;
  background: #fff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 订单清单样式 - 增加左右边距 */
.order-summary {
  margin: 15rpx 20rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.summary-header {
  padding: 15rpx;
  background: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.booking-red-tip {
  color: #ff4444;
  font-size: 26rpx;
  line-height: 1.2;
}

.summary-content {
  padding: 15rpx;
}

.summary-list {
  margin-bottom: 20rpx;
}

.summary-item {
  display: flex;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
}

.summary-item.header {
  font-weight: bold;
  color: #666;
  border-bottom: 2rpx solid #eee;
}

.item-date {
  flex: 0.8;
}
.item-time {
  flex: 0.8;
  text-align: center;
}
.item-people {
  flex: 0.6;
  text-align: center;
}
.item-price {
  flex: 0.6;
  text-align: center;
}
.item-fee {
  flex: 0.8;
  text-align: right;
  color: #ff6633;
}

.reservation-fee-info {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 优惠券选择区域 */
.coupon-section {
  border-top: 1rpx solid #eee;
  background: #fff;
  margin-top: 20rpx;
}

.coupon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.coupon-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.coupon-text {
  color: #999;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.coupon-text.selected {
  color: #4080ff;
}

.coupon-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.clear-all-coupons {
  padding: 20rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.clear-all-text {
  color: #ff6b6b;
  font-size: 26rpx;
}

.coupon-discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 20rpx 30rpx;
  background: #f9f9f9;
}

/* 已选择的优惠券详情 */
.selected-coupons {
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
}

.coupon-item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item-detail:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.coupon-name {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.coupon-type {
  color: #666;
  font-size: 22rpx;
}

.coupon-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.coupon-discount-amount {
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 600;
}

.cancel-coupon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 50%;
  border: 1rpx solid #ddd;
}

.cancel-icon {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}

/* 金额汇总区域 */
.amount-section {
  background: #fff;
  margin-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.subtotal-row,
.discount-row,
.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.total-row {
  border-bottom: none;
  background: #f8f9fa;
  font-weight: 600;
}

.subtotal-label,
.discount-label {
  color: #666;
  font-size: 26rpx;
}

.subtotal-amount {
  color: #333;
  font-size: 26rpx;
}

.discount-label {
  color: #666;
  font-size: 26rpx;
}

.discount-amount {
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 600;
}

.total-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
}

.total-amount {
  color: #ff6b6b;
  font-size: 32rpx;
  font-weight: 700;
}

/* 删除重复的样式定义，使用上面的新样式 */

.summary-actions {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 15rpx;
  border-top: 1rpx solid #eee;
}

.back-btn,
.confirm-order-btn {
  width: 45%;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
}

.back-btn {
  background: #f5f5f5;
  color: #333;
}

.confirm-order-btn {
  background: #4080ff;
  color: #fff;
}

/* 预订费用信息 */
.fee-info {
  padding: 10rpx 15rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  color: #ff6633;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

/* 餐食类型切换样式 */
.meal-type-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0;
  background: #fff;
  border-radius: 12rpx;
  /* 移除 overflow: hidden，避免下拉菜单被裁剪 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 8rpx;
  gap: 8rpx;
  /* 添加相对定位，为下拉菜单提供定位上下文 */
  position: relative;
}

/* 切换到企业餐按钮样式 - 美化版 */
.switch-to-employee-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80rpx;
  box-shadow: 0 2rpx 8rpx rgba(245, 87, 108, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.switch-to-employee-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.switch-to-employee-btn:active::before {
  left: 100%;
}

.switch-to-employee-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 4rpx rgba(245, 87, 108, 0.4);
}

.switch-btn-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.meal-type-tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.meal-type-tab.active {
  color: #07c160;
  font-weight: bold;
}

.meal-type-tab.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background: #07c160;
  border-radius: 3rpx;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 表头样式 */
.table-header {
  display: flex;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: 600;
  font-size: 28rpx;
}

.header-item:nth-child(1) {
  flex: 0.8;
  text-align: center;
}
.header-item:nth-child(2) {
  flex: 0.25;
  text-align: center;
}
.header-item:nth-child(3) {
  flex: 1;
  text-align: center;
}

/* 日期列表样式 */
.date-list {
  margin-top: 10rpx;
}

.date-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.date-item:last-child {
  border-bottom: none;
}

.date {
  flex: 0.8;
  color: #666;
  font-size: 28rpx;
  text-align: center;
  font-weight: 500;
}

/* 人数输入容器样式 */
.people-input-container {
  flex: 0.25;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 15rpx;
  background: transparent;
  border-radius: 0;
  padding: 0;
  border: none;
}

/* 人数输入按钮样式 - 修复居中对齐问题 */
.people-btn {
  width: 32rpx;
  height: 32rpx;
  border: 1rpx solid #4080ff;
  background: #fff;
  color: #4080ff;
  font-size: 20rpx; /* 稍微增大字体 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 6rpx;
  box-sizing: border-box;
  padding: 0;
  line-height: 1;
  font-weight: bold; /* 加粗使符号更清晰 */
  font-family: "Arial", sans-serif; /* 使用无衬线字体确保对齐 */
  text-align: center; /* 额外的文本居中 */
  position: relative; /* 为微调预留空间 */
}

/* 针对减号按钮的微调 */
.people-btn.decrease-btn {
  font-size: 30rpx; /* 减号稍大一些 */
  line-height: 0.8; /* 调整行高 */
}

/* 针对加号按钮的微调 */
.people-btn.increase-btn {
  font-size: 20rpx; /* 加号保持原大小 */
  line-height: 1;
}

.people-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  border-color: #e0e0e0;
}

.people-btn:not(:disabled):active {
  background: #f0f8ff;
}

.people-input {
  width: 62rpx;
  height: 32rpx;
  text-align: center;
  border: 1rpx solid #4080ff;
  border-radius: 16rpx;
  margin: 0 6rpx;
  font-size: 20rpx;
  background: #fff;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  line-height: 1;
}

.people-input:disabled {
  background: #f5f5f5;
  color: #999;
  border-color: #e0e0e0;
}

/* 按钮样式 */
.action-btn {
  margin: 0 10rpx;
  padding: 0 16rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: none;
}

/* 时间段选择按钮样式 */
.select-time-btn {
  flex: 1;
  background: #f0f8ff;
  color: #4080ff;
  border: 1rpx solid #4080ff;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
}

.select-time-btn.active {
  background: #f0f8ff;
  color: #4080ff;
  border: 1rpx solid #4080ff;
}

.select-time-btn.disabled {
  background: #f5f5f5;
  color: #999;
  border: 1rpx solid #ddd;
}

.select-time-btn:active {
  background: #e0f0ff;
}

/* 菜单按钮样式 */
.menu-btn {
  flex: 0.5;
  background: #f0f8ff;
  color: #4080ff;
  border: 1rpx solid #4080ff;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  height: 64rpx;
  line-height: 64rpx;
}

.menu-btn:active {
  background: #e0f0ff;
}

/* 时间段选择区域 */
.time-slot-section {
  margin: 20rpx;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.date-time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  position: relative;
}

.selected-date {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 0;
}

.close-icon {
  font-size: 48rpx;
  color: #999;
  padding: 10rpx;
}

.close-icon:hover {
  background: #e0e0e0;
  transform: scale(1.1);
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  justify-content: flex-start;
}

.time-slot {
  width: calc((100% - 48rpx) / 3);
  padding: 20rpx 10rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  text-align: center;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.time-slot.disabled {
  background: #f5f5f5;
  color: #999;
}

.time-slot.selected {
  background: #4080ff;
  color: white;
  border-color: #4080ff;
}

.remaining {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.time-slot.selected .remaining {
  color: white;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
  width: 45%;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background: #4080ff;
  color: white;
}

/* 支付选项弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.payment-container {
  width: 90%;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-amount {
  color: #000;
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin: 15rpx 0;
}

.payment-methods {
  margin: 20rpx 0;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin: 10rpx 0;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  position: relative;
}

.payment-method.selected {
  border-color: #67c23a;
  background: #f0f9eb;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.balance-text {
  margin-left: 0;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.radio-btn {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 1rpx solid #ddd;
  margin-left: auto;
}

.payment-method.selected .radio-btn {
  border-color: #67c23a;
  background: #67c23a;
  position: relative;
}

.payment-method.selected .radio-btn:after {
  content: "";
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.confirm-payment-btn {
  width: 100%;
  height: 88rpx;
  background: #67c23a;
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
}

.payment-method.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.insufficient-tip {
  color: #ff4d4f;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 下拉选择器样式 */
.dropdown-selector {
  position: relative;
  margin-right: 10rpx;
  /* 确保下拉选择器在最上层 */
  z-index: 1001;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 使用与"请选择"按钮相同的颜色 */
  background: #f0f8ff;
  border: 1rpx solid #4080ff;
  border-radius: 32rpx;
  padding: 16rpx 20rpx;
  min-width: 120rpx;
  box-shadow: 0 2rpx 8rpx rgba(64, 128, 255, 0.2);
  transition: all 0.3s ease;
}

.dropdown-header:active {
  transform: translateY(2rpx);
  background: #e0f0ff;
  box-shadow: 0 1rpx 4rpx rgba(64, 128, 255, 0.3);
}

.dropdown-text {
  font-size: 28rpx;
  color: #4080ff;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.dropdown-arrow {
  font-size: 20rpx;
  color: #4080ff;
  transition: transform 0.3s ease;
  margin-left: 8rpx;
}

.dropdown-arrow.up {
  transform: rotate(180deg);
}

.dropdown-arrow.down {
  transform: rotate(0deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  z-index: 1002;
  opacity: 0;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  pointer-events: none;
  /* 确保下拉菜单不被裁剪 */
  min-width: 120rpx;
  border: 1rpx solid #e0e0e0;
}

.dropdown-menu.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.dropdown-item {
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
  /* 确保文本可见 */
  white-space: nowrap;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background-color: #f0f8ff;
}

.dropdown-item.active {
  background-color: #f0f8ff;
  color: #4080ff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .booking-container {
    padding: 15rpx;
  }

  .date-item {
    padding: 20rpx 15rpx;
  }

  .people-btn {
    width: 28rpx;
    height: 28rpx;
    font-size: 18rpx;
  }

  .people-input {
    width: 50rpx;
    height: 28rpx;
    font-size: 20rpx;
  }

  .select-time-btn {
    font-size: 24rpx;
    padding: 15rpx 20rpx;
  }
}
