/* 弹窗遮罩 */
.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.coupon-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

/* 弹窗容器 */
.coupon-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  padding: 10rpx;
}

/* 标签页 */
.coupon-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 600;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

/* 内容区域 */
.coupon-content {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #4080ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 优惠券列表 */
.coupon-list {
  padding-bottom: 20rpx;
}

/* 优惠券卡片 */
.coupon-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.coupon-item.available {
  border: 2rpx solid transparent;
}

.coupon-item.selected {
  border-color: #4080ff;
}

.coupon-item.disabled {
  opacity: 0.6;
}

/* 优惠券左侧金额区域 */
.coupon-left {
  width: 160rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  position: relative;
}

/* 满减券样式 */
.coupon-item[data-type="DISCOUNT"] .coupon-left {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
}

/* 折扣券样式 */
.coupon-item[data-type="PERCENTAGE"] .coupon-left {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

/* 免运费券样式 */
.coupon-item[data-type="FREE_SHIPPING"] .coupon-left {
  background: linear-gradient(135deg, #45b7d1, #96c93d);
}

.coupon-left::after {
  content: "";
  position: absolute;
  right: -10rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
}

.coupon-amount {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.coupon-condition {
  font-size: 20rpx;
  margin-top: 8rpx;
  opacity: 0.9;
}

/* 优惠券右侧信息区域 */
.coupon-right {
  flex: 1;
  padding: 24rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.coupon-time {
  font-size: 22rpx;
  color: #999;
}

.coupon-unavailable {
  font-size: 22rpx;
  color: #ff6b6b;
  margin-top: 8rpx;
}

/* 选择按钮区域 */
.coupon-select {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.select-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #4080ff;
  background: transparent;
  border: none;
  padding: 0;
}

.select-btn.selected {
  color: #ff4757;
  font-weight: 600;
}

.select-btn.disabled {
  color: #999;
  font-weight: normal;
}

/* 已过期优惠券的按钮样式 */
.select-btn.expired {
  color: #ff6b6b;
  font-weight: normal;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
