Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示优惠券列表
    visible: {
      type: Boolean,
      value: false
    },
    // 订单总金额，用于计算优惠券是否可用
    orderAmount: {
      type: Number,
      value: 0
    },
    // 已选择的优惠券ID
    selectedCouponId: {
      type: String,
      value: ''
    },
    // 是否允许多选
    allowMultiple: {
      type: Boolean,
      value: false
    },
    // 已选择的优惠券ID列表
    selectedCouponIds: {
      type: Array,
      value: []
    },
    // 产品数量列表，用于优惠券计价
    products: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    availableCoupons: [],
    unavailableCoupons: [],
    usedCoupons: [],
    currentTab: 'available', // available, unavailable, used
    tabs: [
      { key: 'available', name: '可使用' },
      { key: 'unavailable', name: '不可用' },
      { key: 'used', name: '已使用' }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 切换标签页
    onTabChange(e) {
      const tab = e.currentTarget.dataset.tab;
      this.setData({
        currentTab: tab
      });
      console.log('当前已选优惠券ID列表:', this.properties.selectedCouponIds);
      console.log('availableCoupons:', this.data.availableCoupons);
      // let r = this.properties.selectedCouponIds.includes(item.coupon_usage_record.id.toString()) ? '取消' : '使用'
      // console.log()
      for (let item of this.data.availableCoupons) {
        console.log('item.coupon_usage_record.id:', item.coupon_usage_record.id);
        console.log('selectedCouponIds.includes:', this.properties.selectedCouponIds.includes(item.coupon_usage_record.id.toString()));
      }
    },

    // 选择优惠券
    onSelectCoupon(e) {
      const coupon = e.currentTarget.dataset.coupon;
      const couponId = coupon.coupon_usage_record.id.toString();

      // 检查优惠券是否可用
      if (!this.isCouponAvailable(coupon)) {
        wx.showToast({
          title: '该优惠券不可用',
          icon: 'none'
        });
        return;
      }

      const currentSelectedIds = this.properties.selectedCouponIds || [];
      if (this.properties.allowMultiple) {
        // 多选模式
        if (currentSelectedIds.includes(couponId)) {
          // 取消选择
          const newSelectedIds = currentSelectedIds.filter(id => id !== couponId);
          this.triggerEvent('select', { 
            couponIds: newSelectedIds,
            coupons: this.getCouponsByIds(newSelectedIds)
          });
        } else {
          // 添加选择
          const newSelectedIds = [...currentSelectedIds, couponId];
          this.triggerEvent('select', { 
            couponIds: newSelectedIds,
            coupons: this.getCouponsByIds(newSelectedIds)
          });
        }
      } else {
        // 单选模式
        if (currentSelectedIds.includes(couponId)) {
          // 取消选择
          this.triggerEvent('select', { 
            couponIds: [],
            coupons: []
          });
        } else {
          // 选择单张
          this.triggerEvent('select', { 
            couponIds: [couponId],
            coupons: [coupon]
          });
        }
      }
    },

    // 根据ID列表获取优惠券对象列表
    getCouponsByIds(couponIds) {
      const allCoupons = [
        ...this.data.availableCoupons,
        ...this.data.unavailableCoupons,
        ...this.data.usedCoupons
      ];

      return allCoupons.filter(coupon =>
        couponIds.includes(coupon.coupon_usage_record.id.toString())
      );
    },

    // 加载优惠券列表
    loadCoupons() {
      console.log('开始加载优惠券列表');
      this.setData({ loading: true });

      const app = getApp();

      // 获取用户ID
      const userInfo = app.globalData.userInfo;
      console.log('用户信息:', userInfo);

      if (!userInfo || !userInfo.id) {
        console.error('用户信息获取失败');
        wx.showToast({
          title: '用户信息获取失败',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      // 准备请求数据
      const requestData = {
        user_id: userInfo.id,
        products: this.properties.products || [],
        coupon_usage_record_ids: this.properties.selectedCouponIds || []
      };

      console.log('请求数据:', requestData);
      console.log('请求URL:', `${app.globalData.baseUrl}/coupon/coupon-pricing`);

      wx.request({
        url: `${app.globalData.baseUrl}/coupon/coupon-pricing`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'token': wx.getStorageSync('token')
        },
        data: requestData,
        success: (res) => {
          console.log('API响应:', res);

          if (res.data && res.data.status === 200) {
            const data = res.data.data;
            console.log('响应数据:', data);

            const couponLists = data.coupon_lists || {};
            console.log('优惠券列表:', couponLists);

            // 预处理优惠券数据，添加格式化的折扣文本
            const processedAvailable = this.processCouponsData(couponLists.available_coupons || []);
            const processedUnavailable = this.processCouponsData(couponLists.unavailable_coupons || []);
            const processedUsed = this.processCouponsData(couponLists.used_coupons || []);

            console.log('处理后的数据:', {
              available: processedAvailable.length,
              unavailable: processedUnavailable.length,
              used: processedUsed.length
            });

            // 为每个优惠券添加选中状态标记
            const availableWithSelection = processedAvailable.map(coupon => ({
              ...coupon,
              isSelected: this.properties.selectedCouponIds.includes(coupon.coupon_usage_record.id.toString())
            }));
            
            this.setData({
              availableCoupons: availableWithSelection,
              unavailableCoupons: processedUnavailable,
              usedCoupons: processedUsed
            });
          } else {
            console.error('API返回错误:', res.data);
            wx.showToast({
              title: res.data?.message || '加载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('加载优惠券失败', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          console.log('请求完成，设置loading为false');
          this.setData({ loading: false });
        }
      });
    },

    // 处理优惠券数据，添加格式化的折扣文本和唯一ID
    processCouponsData(coupons) {
      console.log('处理优惠券数据，输入:', coupons);

      if (!Array.isArray(coupons)) {
        console.error('优惠券数据不是数组:', coupons);
        return [];
      }

      return coupons.map(couponData => {
        console.log('处理单个优惠券:', couponData);

        // 适配新的API数据结构
        const couponType = couponData.coupon_type;
        const couponName = couponData.coupon_name;
        const status = couponData.status;

        let displayText = '';
        let conditionText = '';
        let isExpired = status !== 'valid';

        // 根据优惠券类型设置显示文本
        if (couponType === 'cash') {
          // 现金券
          displayText = `现金券`;
          conditionText = couponName || '现金抵扣';
        } else if (couponType === 'full_reduction') {
          // 满减券
          displayText = `满减券`;
          conditionText = couponName || '满减优惠';
        } else if (couponType === 'discount') {
          // 折扣券
          displayText = `折扣券`;
          conditionText = couponName || '折扣优惠';
        } else {
          // 其他类型
          displayText = '优惠券';
          conditionText = couponName || '';
        }

        // 格式化有效期时间
        let formattedEndTime = '';
        let formattedUsedTime = '';

        if (couponData.valid_end_time) {
          try {
            const endDate = new Date(couponData.valid_end_time);
            formattedEndTime = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;
          } catch (e) {
            formattedEndTime = couponData.valid_end_time;
          }
        }

        if (couponData.used_at) {
          try {
            const usedDate = new Date(couponData.used_at);
            formattedUsedTime = `${usedDate.getFullYear()}-${String(usedDate.getMonth() + 1).padStart(2, '0')}-${String(usedDate.getDate()).padStart(2, '0')}`;
          } catch (e) {
            formattedUsedTime = couponData.used_at;
          }
        }

        // 构造兼容的数据结构
        const processedData = {
          // 保持原有数据
          ...couponData,
          // 添加格式化字段
          displayText: displayText,
          conditionText: conditionText,
          isExpired: isExpired,
          formattedEndTime: formattedEndTime,
          formattedUsedTime: formattedUsedTime,
          uniqueId: couponData.coupon_usage_record_id ? couponData.coupon_usage_record_id.toString() : Math.random().toString(),
          // 添加不可用原因（如果有的话）
          unavailable_reasons: couponData.unavailable_reason ? [couponData.unavailable_reason] : [],
          // 为了兼容现有代码，构造coupon和coupon_usage_record对象
          coupon: {
            id: couponData.coupon_id,
            name: couponData.coupon_name,
            type: couponData.coupon_type,
            description: couponData.coupon_description || couponData.coupon_name
          },
          coupon_usage_record: {
            id: couponData.coupon_usage_record_id,
            used_at: formattedUsedTime
          }
        };
        
        console.log('处理后的优惠券数据:', processedData);
        return processedData;
      });
    },

    // 检查优惠券是否可用
    isCouponAvailable(couponData) {
      const coupon = couponData.coupon;
      const orderAmount = this.properties.orderAmount;

      // 检查是否有不可用原因
      if (couponData.unavailable_reasons && couponData.unavailable_reasons.length > 0) {
        return false;
      }

      // 检查是否过期
      if (couponData.isExpired) {
        return false;
      }

      // 检查金额条件
      if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
        return false;
      }

      return true;
    },

    // 计算优惠券折扣金额
    calculateDiscount(couponData, orderAmount) {
      const coupon = couponData.coupon;

      if (!this.isCouponAvailable(couponData)) {
        return 0;
      }

      if (coupon.type === 'cash') {
        // 现金券：直接返回券面金额
        return coupon.amount;
      } else if (coupon.type === 'full_reduction') {
        // 满减券：检查是否满足满减条件
        if (orderAmount >= coupon.full_amount) {
          return coupon.reduction_amount;
        }
        return 0;
      } else if (coupon.type === 'discount') {
        // 折扣券：检查最低消费，计算折扣金额，限制最大折扣
        if (orderAmount < coupon.min_amount) {
          return 0;
        }
        
        const discountAmount = orderAmount * (1 - coupon.discount_rate);
        return Math.min(discountAmount, coupon.max_discount);
      }

      return 0;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },
    ready() {
      // 组件在视图层布局完成后执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.loadCoupons();
      }
    },
    'selectedCouponIds': function(selectedCouponIds) {
      // 更新可用优惠券的选中状态
      if (this.data.availableCoupons && this.data.availableCoupons.length > 0) {
        const updatedAvailableCoupons = this.data.availableCoupons.map(coupon => ({
          ...coupon,
          isSelected: selectedCouponIds.includes(coupon.coupon_usage_record.id.toString())
        }));
        
        this.setData({
          availableCoupons: updatedAvailableCoupons
        });
      }
    }
  }
});
